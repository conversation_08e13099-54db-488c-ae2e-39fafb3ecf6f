const React = require('react');
const { Eye, Edit, Trash2, Loader2 } = require('lucide-react');
const { Popconfirm, Tooltip } = require('antd');
const { ActionButtons, ActionButton } = require('./StyledComponents');

const ActionButtonGroup = ({
  onView,
  onEdit,
  onDelete,
  deleteConfirmTitle = 'Xác nhận xóa',
  deleteConfirmDescription = 'Bạn có chắc chắn muốn xóa mục này?',
  showView = false,
  showEdit = true,
  showDelete = true,
  viewTooltip = 'Xem chi tiết',
  editTooltip = 'Chỉnh sửa',
  deleteTooltip = 'Xóa',
  viewLoading = false,
  editLoading = false,
  deleteLoading = false,
  disabled = false,
  customActions,
}) => {
  return React.createElement(
    ActionButtons,
    null,
    showView &&
      onView &&
      React.createElement(
        Tooltip,
        { title: viewTooltip },
        React.createElement(
          ActionButton,
          {
            $variant: 'view',
            onClick: onView,
            disabled: disabled || viewLoading,
          },
          viewLoading
            ? React.createElement(Loader2, { className: 'animate-spin' })
            : React.createElement(Eye, null)
        )
      ),
    showEdit &&
      onEdit &&
      React.createElement(
        Tooltip,
        { title: editTooltip },
        React.createElement(
          ActionButton,
          {
            $variant: 'edit',
            onClick: onEdit,
            disabled: disabled || editLoading,
          },
          editLoading
            ? React.createElement(Loader2, { className: 'animate-spin' })
            : React.createElement(Edit, null)
        )
      ),
    showDelete &&
      onDelete &&
      React.createElement(
        Popconfirm,
        {
          title: deleteConfirmTitle,
          description: deleteConfirmDescription,
          onConfirm: onDelete,
          okText: 'Xóa',
          cancelText: 'Hủy',
          okType: 'danger',
          disabled: disabled || deleteLoading,
        },
        React.createElement(
          Tooltip,
          { title: deleteTooltip },
          React.createElement(
            ActionButton,
            {
              $variant: 'delete',
              disabled: disabled || deleteLoading,
            },
            deleteLoading
              ? React.createElement(Loader2, { className: 'animate-spin' })
              : React.createElement(Trash2, null)
          )
        )
      ),
    customActions
  );
};

module.exports = ActionButtonGroup;
