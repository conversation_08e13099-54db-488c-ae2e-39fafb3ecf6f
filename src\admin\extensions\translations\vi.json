{"Analytics": "<PERSON><PERSON> tích", "Auth.components.Oops.text": "<PERSON><PERSON><PERSON> k<PERSON>n của bạn đã bị khóa", "Auth.components.Oops.text.admin": "<PERSON><PERSON><PERSON> có sự nhầm lẫn, h<PERSON><PERSON> liên hệ với người quản trị", "Auth.components.Oops.title": "<PERSON><PERSON><PERSON> t<PERSON>...", "Auth.form.active.label": "<PERSON><PERSON><PERSON> đ<PERSON>", "Auth.form.button.forgot-password": "G<PERSON><PERSON> email", "Auth.form.button.go-home": "QUAY VỀ TRANG CHỦ", "Auth.form.button.login": "<PERSON><PERSON><PERSON>", "Auth.form.button.login.providers.error": "<PERSON><PERSON><PERSON><PERSON> thể kết nối bạn với dịch vụ đã chọn.", "Auth.form.button.login.strapi": "<PERSON><PERSON><PERSON> n<PERSON>p v<PERSON><PERSON>", "Auth.form.button.password-recovery": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> mật kh<PERSON>u", "Auth.form.button.register": "Sẵn sàng để bắt đầu", "Auth.form.confirmPassword.label": "<PERSON><PERSON><PERSON><PERSON> lại mật kh<PERSON>u", "Auth.form.currentPassword.label": "<PERSON><PERSON><PERSON> tại mật kh<PERSON>u", "Auth.form.email.label": "Email", "Auth.form.email.placeholder": "<EMAIL>", "Auth.form.error.blocked": "<PERSON><PERSON><PERSON> khoản của bạn đã bị quản trị viên khóa.", "Auth.form.error.code.provide": "<PERSON><PERSON> đ<PERSON><PERSON><PERSON> cung cấp không ch<PERSON>h xác.", "Auth.form.error.confirmed": "<PERSON><PERSON><PERSON><PERSON>n <PERSON>ail của bạn chưa đư<PERSON><PERSON> xác định.", "Auth.form.error.email.invalid": "<PERSON><PERSON> sai.", "Auth.form.error.email.provide": "<PERSON><PERSON> lòng cung cấp tên đăng nhập hoặc email.", "Auth.form.error.email.taken": "<PERSON><PERSON> đã tồn tại.", "Auth.form.error.invalid": "<PERSON> định danh hoặc mật khẩu.", "Auth.form.error.params.provide": "<PERSON><PERSON> số đư<PERSON><PERSON> cung cấp không ch<PERSON>h xác.", "Auth.form.error.password.format": "<PERSON><PERSON><PERSON> kh<PERSON>u của bạn không thể chứa ký tự ` hơn ba lần", "Auth.form.error.password.local": "Người dùng này chưa từng đặt bộ địa phương mật khẩu, vui lòng đăng nhập thông tin qua nhà cung cấp đã được sử dụng khi tạo đầu tài khoản", "Auth.form.error.password.matching": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp.", "Auth.form.error.password.provide": "<PERSON><PERSON> lòng cung cấp mật kh<PERSON>u của bạn.", "Auth.form.error.ratelimit": "<PERSON><PERSON><PERSON> quá n<PERSON> l<PERSON>, vui lòng thử lại sau một phút", "Auth.form.error.user.not-exist": "<PERSON><PERSON> này không tồn tại.", "Auth.form.error.username.taken": "<PERSON><PERSON><PERSON> đăng nhập đã tồn tại.", "Auth.form.firstname.label": "Họ", "Auth.form.firstname.placeholder": "v.<PERSON>. <PERSON>", "Auth.form.forgot-password.email.label": "Nhập email c<PERSON><PERSON> bạn", "Auth.form.forgot-password.email.label.success": "<PERSON>ail đã đ<PERSON><PERSON><PERSON> gửi thành công", "Auth.form.lastname.label": "<PERSON><PERSON><PERSON>", "Auth.form.lastname.placeholder": "v.<PERSON><PERSON>", "Auth.form.password.hide-password": "Ẩn mật khẩu", "Auth.form.password.hint": "<PERSON><PERSON>t khẩu phải chứa ít nhất 8 ký tự, 1 viết hoa, 1 viết thường và 1 số", "Auth.form.password.show-password": "<PERSON><PERSON><PERSON> thị mật kh<PERSON>u", "Auth.form.register.news.label": "<PERSON><PERSON><PERSON> nhật cho tôi về các chức năng mới và những cải tiến tốt hơn (thông tin về công việc này bạn đã chấp nhận {điều kho<PERSON>n} và {ch<PERSON>h sách}).", "Auth.form.register.subtitle": "Thông tin của bạn chỉ được sử dụng để đăng nhập vào trang quản trị. ", "Auth.form.rememberMe.label": "<PERSON><PERSON>ớ tôi", "Auth.form.username.label": "<PERSON><PERSON><PERSON> đ<PERSON>p", "Auth.form.username.placeholder": "<PERSON>", "Auth.form.welcome.subtitle": "<PERSON><PERSON><PERSON> nhập và<PERSON> tài k<PERSON>n <PERSON><PERSON> của bạn", "Auth.form.welcome.title": "Chào mừng!", "Auth.link.forgot-password": "<PERSON>uên mật khẩu?", "Auth.link.ready": "Sẵn sàng đăng nhập?", "Auth.link.signin": "<PERSON><PERSON><PERSON>", "Auth.link.signin.account": "Đã có tài k<PERSON>n?", "Auth.login.sso.divider": "Hoặc đăng nhập với", "Auth.login.sso.loading": "<PERSON><PERSON> tải cung cấp d<PERSON>ch vụ...", "Auth.login.sso.subtitle": "<PERSON><PERSON><PERSON><PERSON> qua SSO", "Auth.privacy-policy-agreement.policy": "<PERSON><PERSON><PERSON> s<PERSON><PERSON> b<PERSON><PERSON> m<PERSON>t", "Auth.privacy-policy-agreement.terms": "c<PERSON><PERSON> đ<PERSON><PERSON>", "Auth.reset-password.title": "Đặt lại mật khẩu", "Content Manager": "<PERSON><PERSON><PERSON><PERSON>", "Content Type Builder": "<PERSON><PERSON><PERSON><PERSON> lý bảng", "Documentation": "<PERSON><PERSON><PERSON> l<PERSON>", "Email": "Email", "Files Upload": "<PERSON><PERSON><PERSON>", "HomePage.helmet.title": "Trang chủ", "HomePage.roadmap": "<PERSON><PERSON> lộ trình của chúng tôi", "HomePage.welcome.congrats": "<PERSON><PERSON>c mừng!", "HomePage.welcome.congrats.content": "Bạn đã đăng nhập với tư cách là người quản trị đầu tiên. ", "HomePage.welcome.congrats.content.bold": "ch<PERSON>g tôi khuyên bạn nên tạo <PERSON> bộ sưu tập đầu tiên của mình.", "Media Library": "<PERSON><PERSON><PERSON><PERSON>", "New entry": "<PERSON><PERSON><PERSON> ghi mới", "Password": "<PERSON><PERSON><PERSON>", "Provider": "Nhà Cung Cấp", "ResetPasswordToken": "Cài đặt lại Chuỗi mật khẩu", "Role": "<PERSON>ai trò", "Roles & Permissions": "<PERSON>ai trò", "Roles.ListPage.notification.delete-all-not-allowed": "<PERSON><PERSON><PERSON><PERSON> thể xóa một số vai trò vì chúng đư<PERSON><PERSON> liên kết với người dùng", "Roles.ListPage.notification.delete-not-allowed": "<PERSON><PERSON><PERSON><PERSON> thể xóa vai trò nếu đư<PERSON><PERSON> liên kết với người dùng", "Roles.RoleRow.select-all": "<PERSON><PERSON><PERSON> {name} cho hành động hàng loạt", "Roles.RoleRow.user-count": "{number, plural, =0 {# user} one {# user} other {# users}}", "Roles.components.List.empty.withSearch": "<PERSON><PERSON><PERSON>ng có vai trò nào tương ứng với tìm kiếm ({search})...", "Settings.PageTitle": "<PERSON><PERSON>i đặt — {name}", "Settings.apiTokens.ListView.headers.createdAt": "<PERSON><PERSON><PERSON><PERSON> tạo vào lúc", "Settings.apiTokens.ListView.headers.description": "<PERSON><PERSON>", "Settings.apiTokens.ListView.headers.lastUsedAt": "Lần sử dụng cuối cùng", "Settings.apiTokens.ListView.headers.name": "<PERSON><PERSON><PERSON>", "Settings.apiTokens.ListView.headers.type": "Token type", "Settings.apiTokens.addFirstToken": "Thêm API Token đầu tiên của bạn", "Settings.apiTokens.addNewToken": "Thêm API Token mới", "Settings.apiTokens.create": "Tạo API Token mới", "Settings.apiTokens.createPage.BoundRoute.title": "<PERSON><PERSON><PERSON><PERSON> đư<PERSON><PERSON> ràng buộc tới", "Settings.apiTokens.createPage.permissions.description": "Chỉ những hành động bị ràng buộc bởi một tuyến đường mới được liệt kê bên dưới.", "Settings.apiTokens.createPage.permissions.header.hint": "Chọn hành động của ứng dụng hoặc hành động của plugin và nhấp vào biểu tượng răng cưa để hiển thị tuyến đường bị ràng buộc", "Settings.apiTokens.createPage.permissions.header.title": "Cài đặt nâng cao", "Settings.apiTokens.createPage.permissions.title": "<PERSON><PERSON><PERSON><PERSON>", "Settings.apiTokens.createPage.title": "Tạo API Token", "Settings.apiTokens.description": "<PERSON><PERSON> sách mã thông báo đư<PERSON><PERSON> tạo để sử dụng API", "Settings.apiTokens.emptyStateLayout": "Bạn chưa có bất kỳ nội dung nào...", "Settings.apiTokens.regenerate": "t<PERSON>i sinh", "Settings.apiTokens.title": "API Token", "Settings.apiTokens.lastHour": "gi<PERSON> cuối cùng", "Settings.application.customization": "<PERSON><PERSON><PERSON> chỉnh", "Settings.application.customization.auth-logo.carousel-hint": "Thay thế logo trong các trang xác thực", "Settings.application.customization.carousel-hint": "Thay đổi logo bảng quản trị (<PERSON><PERSON><PERSON> thước tối đa: {dimension}x{dimension}, <PERSON><PERSON><PERSON> thước tệp tối đa: {size}KB)", "Settings.application.customization.carousel-slide.label": "Logo slide", "Settings.application.customization.carousel.auth-logo.title": "<PERSON>go x<PERSON>c thực", "Settings.application.customization.carousel.change-action": "Thay đổi logo", "Settings.application.customization.carousel.menu-logo.title": "Menu logo", "Settings.application.customization.carousel.reset-action": "Đặt lại logo", "Settings.application.customization.carousel.title": "Logo", "Settings.application.customization.menu-logo.carousel-hint": "Thay thế logo trong điều hướ<PERSON> ch<PERSON>h", "Settings.application.customization.modal.cancel": "Hủy bỏ", "Settings.application.customization.modal.pending": "Pending logo", "Settings.application.customization.modal.pending.card-badge": "<PERSON><PERSON><PERSON>", "Settings.application.customization.modal.pending.choose-another": "Chọn một logo kh<PERSON>c", "Settings.application.customization.modal.pending.subtitle": "<PERSON><PERSON><PERSON><PERSON> lý logo đã chọn trư<PERSON>c khi tải lên", "Settings.application.customization.modal.pending.title": "Logo đã sẵn sàng để tải lên", "Settings.application.customization.modal.pending.upload": "<PERSON><PERSON><PERSON> lê<PERSON> logo", "Settings.application.customization.modal.tab.label": "Bạn muốn tải nội dung của mình lên bằng cách nào?", "Settings.application.customization.modal.upload": "<PERSON><PERSON><PERSON> lê<PERSON> logo", "Settings.application.customization.modal.upload.cta.browse": "<PERSON><PERSON><PERSON><PERSON> tập tin", "Settings.application.customization.modal.upload.drag-drop": "<PERSON><PERSON>o thả vào đây hoặc", "Settings.application.customization.modal.upload.error-format": "<PERSON><PERSON> tải lên sai định dạng (chỉ những định dạng được chấp nhận: jpeg, jpg, png, svg).", "Settings.application.customization.modal.upload.error-network": "Lỗi mạng", "Settings.application.customization.modal.upload.error-size": "Tệp đã tải lên quá lớn (kích thước tối đa: {dimension}x{dimension}, kích thước tệp tối đa: {size}KB)", "Settings.application.customization.modal.upload.file-validation": "<PERSON><PERSON><PERSON> thước tối đa: {dimension}x{dimension}, <PERSON><PERSON><PERSON> thước tối đa: {size}KB", "Settings.application.customization.modal.upload.from-computer": "<PERSON>ừ máy t<PERSON>h", "Settings.application.customization.modal.upload.from-url": "Từ URL", "Settings.application.customization.modal.upload.from-url.input-label": "URL", "Settings.application.customization.modal.upload.next": "<PERSON><PERSON> tiếp", "Settings.application.customization.size-details": "<PERSON><PERSON><PERSON> thước tối đa: {dimension}×{dimension}, <PERSON><PERSON><PERSON> thước tệp tối đa: {size}KB", "Settings.application.description": "Thông tin toàn cầu của bảng quản trị", "Settings.application.edition-title": "<PERSON><PERSON> hoạch hiện tại", "Settings.application.ee-or-ce": "{communityEdition, select, true {Community Edition} other {Enterprise Edition}}", "Settings.application.ee.admin-seats.add-seats": "{isHostedOnStrapiCloud, select, true {Add seats} other {Contact sales}}", "Settings.application.ee.admin-seats.at-limit-tooltip": "Đạt giới hạn: thêm chỗ ngồi để mời thêm người dùng", "Settings.application.ee.admin-seats.count": "<text>{enforcementUserCount}</text>/{permittedSeats}", "Settings.application.get-help": "<PERSON><PERSON>n gi<PERSON>p đỡ", "Settings.application.link-pricing": "<PERSON><PERSON> tất cả các gói giá", "Settings.application.link-upgrade": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> bảng quản trị của bạn", "Settings.application.node-version": "node version", "Settings.application.strapi-version": "<PERSON><PERSON><PERSON> b<PERSON>", "Settings.application.strapiVersion": "<PERSON><PERSON><PERSON> b<PERSON>", "Settings.application.title": "<PERSON><PERSON><PERSON> quan", "Settings.error": "Lỗi", "Settings.global": "<PERSON><PERSON><PERSON><PERSON> lập tổng quát", "Settings.permissions": "<PERSON><PERSON><PERSON> quản trị", "Settings.permissions.auditLogs.action": "<PERSON><PERSON><PERSON> đ<PERSON>", "Settings.permissions.auditLogs.admin.auth.success": "<PERSON><PERSON><PERSON> nh<PERSON>p quản trị viên", "Settings.permissions.auditLogs.admin.logout": "<PERSON><PERSON><PERSON> xuất quản trị viên", "Settings.permissions.auditLogs.component.create": "<PERSON><PERSON><PERSON> thành phần", "Settings.permissions.auditLogs.component.delete": "<PERSON><PERSON><PERSON> thành p<PERSON>n", "Settings.permissions.auditLogs.component.update": "<PERSON><PERSON><PERSON> nh<PERSON>t thành phần", "Settings.permissions.auditLogs.content-type.create": "<PERSON><PERSON><PERSON> ki<PERSON>u nội dung", "Settings.permissions.auditLogs.content-type.delete": "<PERSON><PERSON><PERSON> lo<PERSON>i nội dung", "Settings.permissions.auditLogs.content-type.update": "<PERSON><PERSON><PERSON> nh<PERSON>t lo<PERSON>i nội dung", "Settings.permissions.auditLogs.date": "<PERSON><PERSON><PERSON>", "Settings.permissions.auditLogs.details": "<PERSON> tiết nh<PERSON>t ký", "Settings.permissions.auditLogs.entry.create": "<PERSON><PERSON><PERSON> b<PERSON> ghi{model, select, undefined {} other { ({model})}}", "Settings.permissions.auditLogs.entry.delete": "<PERSON><PERSON><PERSON> b<PERSON> ghi{model, select, undefined {} other { ({model})}}", "Settings.permissions.auditLogs.entry.publish": "<PERSON><PERSON><PERSON> bản mục {model, select, undefined {} other {({model})}}", "Settings.permissions.auditLogs.entry.unpublish": "<PERSON><PERSON><PERSON> xu<PERSON><PERSON> bản mục{model, select, undefined {} other { ({model})}}", "Settings.permissions.auditLogs.entry.update": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> bản ghi{model, select, undefined {} other { ({model})}}", "Settings.permissions.auditLogs.filters.combobox.aria-label": "<PERSON><PERSON><PERSON> kiếm và chọn một tùy chọn để lọc", "Settings.permissions.auditLogs.listview.header.subtitle": "<PERSON><PERSON><PERSON><PERSON> ký của tất cả các hoạt động xảy ra trong môi trường của bạn", "Settings.permissions.auditLogs.media.create": "Tạo media", "Settings.permissions.auditLogs.media.delete": "Xóa media", "Settings.permissions.auditLogs.media.update": "Cập n<PERSON> media", "Settings.permissions.auditLogs.payload": "Payload", "Settings.permissions.auditLogs.permission.create": "<PERSON><PERSON><PERSON> q<PERSON>", "Settings.permissions.auditLogs.permission.delete": "<PERSON><PERSON><PERSON> q<PERSON>", "Settings.permissions.auditLogs.permission.update": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> quyền", "Settings.permissions.auditLogs.role.create": "Tạo vai trò", "Settings.permissions.auditLogs.role.delete": "Xóa vai trò", "Settings.permissions.auditLogs.role.update": "<PERSON><PERSON><PERSON> nhật vai trò", "Settings.permissions.auditLogs.user": "<PERSON><PERSON><PERSON><PERSON> dùng", "Settings.permissions.auditLogs.user.create": "<PERSON><PERSON><PERSON> dùng", "Settings.permissions.auditLogs.user.delete": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> dùng", "Settings.permissions.auditLogs.user.fullname": "{firstname} {lastname}", "Settings.permissions.auditLogs.user.update": "<PERSON><PERSON><PERSON> nh<PERSON>t ng<PERSON><PERSON> dùng", "Settings.permissions.auditLogs.userId": "ID người dùng", "Settings.permissions.category": "<PERSON><PERSON>i đặt quyền cho {category}", "Settings.permissions.category.plugins": "Cài đặt quyền cho plugin {category}", "Settings.permissions.conditions.anytime": "<PERSON><PERSON><PERSON> cứ lúc nào", "Settings.permissions.conditions.apply": "<PERSON><PERSON>", "Settings.permissions.conditions.can": "<PERSON><PERSON> thể", "Settings.permissions.conditions.conditions": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>n", "Settings.permissions.conditions.define-conditions": "<PERSON><PERSON><PERSON> đ<PERSON>nh điều kiện", "Settings.permissions.conditions.links": "<PERSON><PERSON><PERSON>", "Settings.permissions.conditions.no-actions": "Trước tiên bạn cần chọn các hành động (tạo, đọc, cập nhật, ...) trước khi xác định điều kiện cho chúng.", "Settings.permissions.conditions.none-selected": "<PERSON><PERSON><PERSON> cứ lúc nào", "Settings.permissions.conditions.or": "HOẶC", "Settings.permissions.conditions.when": "<PERSON><PERSON>", "Settings.permissions.select-all-by-permission": "<PERSON><PERSON><PERSON> tất cả quyền {label}", "Settings.permissions.select-by-permission": "<PERSON><PERSON><PERSON> quyền {label}", "Settings.permissions.users.active": "<PERSON><PERSON><PERSON> đ<PERSON>", "Settings.permissions.users.create": "<PERSON>ời người dùng mới", "Settings.permissions.users.email": "Email", "Settings.permissions.users.firstname": "<PERSON><PERSON><PERSON>", "Settings.permissions.users.form.sso": "<PERSON><PERSON><PERSON> n<PERSON> với SSO", "Settings.permissions.users.sso.provider.error": "<PERSON><PERSON> xảy ra lỗi khi yêu cầu cài đặt SSO", "Settings.permissions.users.form.sso.description": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> b<PERSON> (ON), ng<PERSON><PERSON><PERSON> dùng có thể đăng nhập qua SSO", "Settings.permissions.users.inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "Settings.permissions.users.lastname": "Họ", "Settings.permissions.users.listview.header.subtitle": "Tất cả người dùng có quyền truy cập vào bảng quản trị <PERSON>", "Settings.permissions.users.roles": "<PERSON>ai trò", "Settings.permissions.users.strapi-author": "Tác g<PERSON>", "Settings.permissions.users.strapi-editor": "<PERSON><PERSON><PERSON><PERSON> tập viên", "Settings.permissions.users.strapi-super-admin": "<PERSON><PERSON><PERSON><PERSON> trị viên cấp cao", "Settings.permissions.users.tabs.label": "Tabs Permissions", "Settings.permissions.users.user-status": "<PERSON>r<PERSON>ng thái người dùng", "Settings.permissions.users.username": "Username", "Settings.profile.form.notify.data.loaded": "<PERSON><PERSON> liệu hồ sơ của bạn đã đ<PERSON><PERSON><PERSON> tải", "Settings.profile.form.section.experience.clear.select": "<PERSON><PERSON><PERSON> ngôn ngữ giao diện đã chọn", "Settings.profile.form.section.experience.here": "<PERSON><PERSON><PERSON>", "Settings.profile.form.section.experience.interfaceLanguage": "<PERSON><PERSON><PERSON> ng<PERSON> giao <PERSON>", "Settings.profile.form.section.experience.interfaceLanguage.hint": "<PERSON><PERSON><PERSON>u này sẽ chỉ hiển thị giao diện của riêng bạn bằng ngôn ngữ đã chọn.", "Settings.profile.form.section.experience.interfaceLanguageHelp": "<PERSON><PERSON><PERSON><PERSON> thay đổi về ưu tiên sẽ chỉ áp dụng cho bạn. ", "Settings.profile.form.section.experience.mode.hint": "Hiển thị giao diện của bạn ở chế độ đã chọn.", "Settings.profile.form.section.experience.mode.label": "<PERSON><PERSON> độ giao <PERSON>n", "Settings.profile.form.section.experience.mode.option-label": "<PERSON><PERSON> độ {name}", "Settings.profile.form.section.experience.title": "<PERSON><PERSON>", "Settings.profile.form.section.helmet.title": "Thông tin người dùng", "Settings.profile.form.section.profile.page.title": "<PERSON><PERSON> hồ sơ", "Settings.review-workflows.page.title": "<PERSON>em lại quy trình công việc", "Settings.review-workflows.page.subtitle": "{count, plural, one {# stage} other {# stages}}", "Settings.review-workflows.page.isLoading": "<PERSON><PERSON> tải quy trình làm việc", "Settings.review-workflows.page.delete.confirm.body": "Tất cả các mục được gán cho các giai đoạn đã xóa sẽ được chuyển sang giai đoạn trước đó. ", "Settings.review-workflows.stage.name.label": "<PERSON><PERSON><PERSON> giai đo<PERSON>n", "Settings.roles.create.description": "<PERSON><PERSON><PERSON> đ<PERSON>nh các quyền đ<PERSON><PERSON><PERSON> trao cho vai trò", "Settings.roles.create.title": "<PERSON><PERSON>o một vai trò", "Settings.roles.created": "Đã tạo vai trò", "Settings.roles.edit.title": "Chỉnh sửa vai trò", "Settings.roles.form.button.users-with-role": "{number, plural, =0 {# users} one {# user} other {# users}} with this role", "Settings.roles.form.created": "Tạo", "Settings.roles.form.description": "Tên và mô tả vai trò", "Settings.roles.form.permission.property-label": "quyền {label}", "Settings.roles.form.permissions.attributesPermissions": "Quy<PERSON>n của fields", "Settings.roles.form.permissions.create": "<PERSON><PERSON><PERSON>", "Settings.roles.form.permissions.delete": "Xóa bỏ", "Settings.roles.form.permissions.publish": "<PERSON><PERSON><PERSON>", "Settings.roles.form.permissions.read": "<PERSON><PERSON><PERSON>", "Settings.roles.form.permissions.update": "<PERSON><PERSON><PERSON>", "Settings.roles.list.button.add": "Thê<PERSON> vai trò mới", "Settings.roles.list.description": "<PERSON><PERSON> s<PERSON>ch vai trò", "Settings.roles.title.singular": "vai trò", "Settings.sso.description": "<PERSON><PERSON><PERSON> cấu hình cài đặt cho tính năng Single Sign-On.", "Settings.sso.form.defaultRole.description": "<PERSON><PERSON> sẽ đính kèm người dùng được xác thực mới vào vai trò đã chọn", "Settings.sso.form.defaultRole.description-not-allowed": "Bạn cần có quyền đọc vai trò quản trị viên", "Settings.sso.form.defaultRole.label": "<PERSON>ai trò mặc định", "Settings.sso.form.localAuthenticationLock.label": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>c thực c<PERSON>c bộ", "Settings.sso.form.localAuthenticationLock.description": "<PERSON>ọn vai trò mà bạn muốn tắt xác thực cục bộ", "Settings.sso.form.registration.description": "Tạo người dùng mới khi đăng nhập SSO nếu không có tài khoản nào tồn tại", "Settings.sso.form.registration.label": "Tự động đăng ký", "Settings.sso.title": "Single Sign-On", "Settings.tokens.Button.cancel": "Hủy bỏ", "Settings.tokens.Button.regenerate": "<PERSON><PERSON><PERSON> t<PERSON>o", "Settings.tokens.ListView.headers.createdAt": "<PERSON><PERSON><PERSON><PERSON> tạo vào lúc", "Settings.tokens.ListView.headers.description": "<PERSON><PERSON>", "Settings.tokens.ListView.headers.lastUsedAt": "Lần sử dụng cuối cùng", "Settings.tokens.ListView.headers.name": "<PERSON><PERSON><PERSON>", "Settings.tokens.RegenerateDialog.title": "Tạo lạ<PERSON>", "Settings.tokens.copy.editMessage": "<PERSON><PERSON> lý do bảo mật, bạn chỉ có thể xem Token của mình một lần.", "Settings.tokens.copy.editTitle": "Token này không thể truy cập đ<PERSON><PERSON><PERSON> n<PERSON>.", "Settings.tokens.copy.lastWarning": "<PERSON><PERSON><PERSON> b<PERSON>o sao ch<PERSON> nà<PERSON>, bạn sẽ không thể nhìn thấy nó nữa!", "Settings.tokens.duration.30-days": "30 ngày", "Settings.tokens.duration.7-days": "7 ngày", "Settings.tokens.duration.90-days": "90 ngày", "Settings.tokens.duration.expiration-date": "<PERSON><PERSON><PERSON> h<PERSON> hạn", "Settings.tokens.duration.unlimited": "<PERSON><PERSON> h<PERSON>", "Settings.tokens.form.description": "<PERSON><PERSON>", "Settings.tokens.form.duration": "<PERSON><PERSON>ờ<PERSON> l<PERSON>", "Settings.tokens.form.name": "<PERSON><PERSON><PERSON>", "Settings.tokens.form.type": "<PERSON><PERSON><PERSON>", "Settings.tokens.notification.copied": "Đã sao chép Token vào bảng nhớ tạm.", "Settings.tokens.popUpWarning.message": "Bạn có chắc chắn muốn tạo lại Token này không?", "Settings.tokens.regenerate": "<PERSON><PERSON><PERSON> lạ<PERSON>", "Settings.tokens.types.custom": "<PERSON><PERSON>", "Settings.tokens.types.full-access": "<PERSON><PERSON><PERSON> quyền truy cập", "Settings.tokens.types.read-only": "Chỉ đọc", "Settings.transferTokens.ListView.headers.type": "<PERSON><PERSON><PERSON>", "Settings.transferTokens.addFirstToken": "<PERSON><PERSON><PERSON><PERSON> chuyển giao đầu tiên của bạn", "Settings.transferTokens.addNewToken": "<PERSON><PERSON><PERSON><PERSON> chuyển mới", "Settings.transferTokens.create": "<PERSON><PERSON>o <PERSON> chuyển mới", "Settings.transferTokens.createPage.title": "<PERSON><PERSON><PERSON> chuyển", "Settings.transferTokens.description": "<PERSON><PERSON> s<PERSON>ch <PERSON> chuyển đ<PERSON><PERSON><PERSON> tạo", "Settings.transferTokens.emptyStateLayout": "Bạn chưa có bất kỳ nội dung nào...", "Settings.transferTokens.title": "<PERSON><PERSON><PERSON><PERSON>", "Settings.webhooks.create": "<PERSON><PERSON><PERSON> một webhook", "Settings.webhooks.create.header": "Tạo tiêu đề mới", "Settings.webhooks.created": "Đã tạo webhook", "Settings.webhooks.event.publish-tooltip": "Sự kiện này chỉ tồn tại đối với những nội dung đã bật hệ thống Bản nháp/<PERSON><PERSON><PERSON> bản", "Settings.webhooks.event.select": "<PERSON><PERSON><PERSON> sự kiện", "Settings.webhooks.events.isLoading": "<PERSON><PERSON> tải sự kiện", "Settings.webhooks.events.create": "<PERSON><PERSON><PERSON>", "Settings.webhooks.events.update": "<PERSON><PERSON><PERSON>", "Settings.webhooks.events.delete": "Xóa webhook", "Settings.webhooks.form.events": "<PERSON><PERSON> kiện", "Settings.webhooks.form.headers": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "Settings.webhooks.form.url": "URL", "Settings.webhooks.headers.remove": "<PERSON><PERSON><PERSON> hàng tiêu đề {number}", "Settings.webhooks.key": "<PERSON><PERSON><PERSON>", "Settings.webhooks.list.button.add": "<PERSON><PERSON>o webhook mới", "Settings.webhooks.list.description": "<PERSON><PERSON><PERSON><PERSON> thông báo thay đổi POST", "Settings.webhooks.list.empty.description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy webhook", "Settings.webhooks.list.empty.link": "<PERSON>em tài li<PERSON>u của chúng tôi", "Settings.webhooks.list.empty.title": "Chưa có webhook nào", "Settings.webhooks.list.th.actions": "<PERSON><PERSON><PERSON> động", "Settings.webhooks.list.th.status": "trạng thái", "Settings.webhooks.list.loading.success": "Webhooks đã đ<PERSON><PERSON><PERSON> tải", "Settings.webhooks.singular": "webhook", "Settings.webhooks.title": "Webhooks", "Settings.webhooks.to.delete": "{webhooksToDeleteLength, plural, one {# webhook} other {# webhooks}} selected", "Settings.webhooks.trigger": "<PERSON><PERSON>", "Settings.webhooks.trigger.cancel": "<PERSON><PERSON><PERSON> k<PERSON>", "Settings.webhooks.trigger.pending": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> quy<PERSON>…", "Settings.webhooks.trigger.save": "<PERSON><PERSON> lòng lưu để kích ho<PERSON>t", "Settings.webhooks.trigger.success": "Th<PERSON>nh công!", "Settings.webhooks.trigger.success.label": "<PERSON><PERSON><PERSON> ho<PERSON>t thành công", "Settings.webhooks.trigger.test": "<PERSON><PERSON><PERSON> ho<PERSON> thử nghiệm", "Settings.webhooks.trigger.title": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> khi kích ho<PERSON>", "Settings.webhooks.value": "<PERSON><PERSON><PERSON> trị", "Settings.webhooks.validation.name.required": "<PERSON><PERSON><PERSON> là b<PERSON><PERSON> bu<PERSON>c", "Settings.webhooks.validation.name.regex": "<PERSON><PERSON><PERSON> ph<PERSON>i bắt đầu bằng một chữ cái và chỉ chứa các chữ cái, số, dấu cách và dấu gạch dưới", "Settings.webhooks.validation.url.required": "<PERSON><PERSON> là b<PERSON><PERSON> bu<PERSON>c", "Settings.webhooks.validation.url.regex": "<PERSON><PERSON><PERSON> trị phải là Url hợp lệ", "Settings.webhooks.validation.key": "<PERSON><PERSON><PERSON> c<PERSON> key", "Settings.webhooks.validation.value": "<PERSON><PERSON><PERSON> trị là b<PERSON>t buộc", "Usecase.back-end": "<PERSON><PERSON><PERSON> ph<PERSON>t triển back-end", "Usecase.button.skip": "Bỏ qua câu hỏi này", "Usecase.content-creator": "Tác g<PERSON>", "Usecase.front-end": "<PERSON><PERSON><PERSON> ph<PERSON>t triển front-end", "Usecase.full-stack": "<PERSON><PERSON><PERSON> trình viên full stack", "Usecase.input.work-type": "bạn làm loại công việc gì?", "Usecase.notification.success.project-created": "Dự án đã đư<PERSON><PERSON> tạo thành công", "Usecase.other": "K<PERSON><PERSON><PERSON>", "Usecase.title": "<PERSON><PERSON><PERSON> cho chúng tôi biết thêm một chút về bản thân bạn", "Username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "Users & Permissions": "User and <PERSON><PERSON> quyền", "Users": "User", "Users.components.List.empty": "<PERSON><PERSON><PERSON><PERSON> có người dùng...", "Users.components.List.empty.withFilters": "<PERSON><PERSON><PERSON><PERSON> có người dùng nào có bộ lọc được áp dụng...", "Users.components.List.empty.withSearch": "<PERSON><PERSON><PERSON><PERSON> có người dùng tương ứng với tìm kiếm ({search})...", "admin.pages.MarketPlacePage.sort.label": "<PERSON><PERSON><PERSON> xếp theo", "admin.pages.MarketPlacePage.filters.categories": "<PERSON><PERSON><PERSON> lo<PERSON>", "admin.pages.MarketPlacePage.filters.categoriesSelected": "{count, plural, =0 {No categories} one {# category} other {# categories}} selected", "admin.pages.MarketPlacePage.filters.collections": "<PERSON><PERSON> s<PERSON>u tập", "admin.pages.MarketPlacePage.filters.collectionsSelected": "{count, plural, =0 {No Collections} one {# Collection} other {# Collections}} selected", "admin.pages.MarketPlacePage.helmet": "<PERSON><PERSON><PERSON> tr<PERSON> - <PERSON><PERSON><PERSON>", "admin.pages.MarketPlacePage.missingPlugin.description": "<PERSON><PERSON><PERSON> cho chúng tôi biết bạn đang tìm kiếm plugin nào và chúng tôi sẽ cho các nhà phát triển plugin cộng đồng của chúng tôi biết trong trường hợp họ đang tìm kiếm nguồn cảm hứng!", "admin.pages.MarketPlacePage.missingPlugin.title": "<PERSON><PERSON><PERSON><PERSON> một plugin?", "admin.pages.MarketPlacePage.offline.subtitle": "<PERSON><PERSON><PERSON> cần kết nối Internet để truy cập <PERSON>.", "admin.pages.MarketPlacePage.offline.title": "Bạn đang offline", "admin.pages.MarketPlacePage.plugin.copy": "<PERSON><PERSON> chép lệnh cài đặt", "admin.pages.MarketPlacePage.plugin.copy.success": "Lệnh cài đặt sẵn sàng để dán vào terminal của bạn", "admin.pages.MarketPlacePage.plugin.downloads": "Plugin này có {downloadsCount} l<PERSON><PERSON><PERSON> tải xuống hàng tuần", "admin.pages.MarketPlacePage.plugin.githubStars": "Plugin này đã đư<PERSON>c gắn dấu sao {starsCount} trên <PERSON>", "admin.pages.MarketPlacePage.plugin.info": "<PERSON><PERSON><PERSON> hi<PERSON>u thêm", "admin.pages.MarketPlacePage.plugin.info.label": "<PERSON><PERSON>m hiểu thêm về {pluginName}", "admin.pages.MarketPlacePage.plugin.info.text": "H<PERSON><PERSON>", "admin.pages.MarketPlacePage.plugin.installed": "Cài đặt", "admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi": "<PERSON><PERSON><PERSON><PERSON> thực hiện bởi Strapi", "admin.pages.MarketPlacePage.plugin.tooltip.verified": "<PERSON>lug<PERSON> đ<PERSON><PERSON> xác <PERSON>h bởi Strap<PERSON>", "admin.pages.MarketPlacePage.plugin.version": "<PERSON><PERSON><PERSON> nh<PERSON>t phiên bản <PERSON><PERSON> của bạn: \"{strapiAppVersion}\" thành: \"{versionRange}\"", "admin.pages.MarketPlacePage.plugin.version.null": "<PERSON><PERSON><PERSON><PERSON> thể xác minh tính tương thích với phiên bản <PERSON><PERSON>i của bạn: \"{strapiAppVersion}\"", "admin.pages.MarketPlacePage.plugins": "b<PERSON> sung", "admin.pages.MarketPlacePage.provider.downloads": "<PERSON><PERSON><PERSON> cung cấp này có {downloadsCount} l<PERSON><PERSON><PERSON> tải xuống hàng tuần", "admin.pages.MarketPlacePage.provider.githubStars": "<PERSON><PERSON><PERSON> cung cấp này đã được gắn dấu sao {starsCount} trê<PERSON>", "admin.pages.MarketPlacePage.providers": "<PERSON><PERSON><PERSON> cung cấp", "admin.pages.MarketPlacePage.search.clear": "<PERSON><PERSON><PERSON> t<PERSON>m k<PERSON>m", "admin.pages.MarketPlacePage.search.empty": "<PERSON><PERSON><PERSON><PERSON> có kết quả cho \"{target}\"", "admin.pages.MarketPlacePage.search.placeholder": "<PERSON><PERSON><PERSON>", "admin.pages.MarketPlacePage.sort.alphabetical": "<PERSON><PERSON><PERSON> tự ABC", "admin.pages.MarketPlacePage.sort.alphabetical.selected": "<PERSON><PERSON><PERSON> xếp theo thứ tự bảng chữ cái", "admin.pages.MarketPlacePage.sort.githubStars": "Số lượng sao GitHub", "admin.pages.MarketPlacePage.sort.githubStars.selected": "<PERSON><PERSON><PERSON> xếp theo sao Git<PERSON>ub", "admin.pages.MarketPlacePage.sort.newest": "<PERSON><PERSON><PERSON>", "admin.pages.MarketPlacePage.sort.newest.selected": "<PERSON><PERSON><PERSON> xế<PERSON> theo mới nh<PERSON>t", "admin.pages.MarketPlacePage.sort.npmDownloads": "S<PERSON> l<PERSON><PERSON> tải xu<PERSON>", "admin.pages.MarketPlacePage.sort.npmDownloads.selected": "<PERSON><PERSON><PERSON> xếp theo <PERSON> tải xuống npm", "admin.pages.MarketPlacePage.submit.plugin.link": "Gửi plugin", "admin.pages.MarketPlacePage.submit.provider.link": "<PERSON><PERSON><PERSON> nhà cung cấp", "admin.pages.MarketPlacePage.subtitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> nhiều hơn từ <PERSON>rapi", "admin.pages.MarketPlacePage.tab-group.label": "Plugin và nhà cung cấp cho Strap<PERSON>", "anErrorOccurred": "<PERSON><PERSON>t ti<PERSON>! ", "app.component.CopyToClipboard.label": "Sao chép vào clipboard", "app.component.search.label": "<PERSON><PERSON><PERSON> k<PERSON> {target}", "app.component.table.duplicate": "<PERSON><PERSON><PERSON> đôi {target}", "app.component.table.edit": "Chỉnh sửa {target}", "app.component.table.read": "<PERSON><PERSON><PERSON> {target}", "app.component.table.select.one-entry": "<PERSON><PERSON><PERSON> {target}", "app.component.table.view": "chi ti<PERSON><PERSON> về {target}", "app.components.BlockLink.blog": "Blog", "app.components.BlockLink.blog.content": "<PERSON><PERSON><PERSON> tin tức mới nhất về Strapi và hệ sinh thái.", "app.components.BlockLink.cloud": "Strapi Cloud", "app.components.BlockLink.cloud.content": "<PERSON>ột nền tảng cộng tác và có thể kết hợp hoàn toàn để tăng tốc độ nhóm của bạn.", "app.components.BlockLink.code": "<PERSON>í dụ mã", "app.components.BlockLink.code.content": "<PERSON><PERSON><PERSON> hiểu bằng cách thử nghiệm các dự án thực tế được phát triển bởi cộng đồng.", "app.components.BlockLink.documentation.content": "<PERSON><PERSON><PERSON><PERSON> phá các khá<PERSON>, hướng dẫn và hướng dẫn cần thiết.", "app.components.BlockLink.tutorial": "Hướng dẫn", "app.components.BlockLink.tutorial.content": "<PERSON><PERSON><PERSON> theo hướng dẫn từng bước để sử dụng và tùy chỉnh Strapi.", "app.components.Button.cancel": "Abort", "app.components.Button.confirm": "<PERSON><PERSON><PERSON>", "app.components.Button.reset": "<PERSON><PERSON>i lại", "app.components.ComingSoonPage.comingSoon": "<PERSON><PERSON><PERSON> x<PERSON>p có", "app.components.ConfirmDialog.title": "<PERSON><PERSON><PERSON>", "app.components.DownloadInfo.download": "<PERSON><PERSON> tả<PERSON> xu<PERSON>", "app.components.DownloadInfo.text": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON> này có thể tốn một phút. ", "app.components.EmptyAttributes.title": "No input field", "app.components.EmptyStateLayout.content-document": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nội dung", "app.components.EmptyStateLayout.content-permissions": "Bạn không có quyền truy cập nội dung đó", "app.components.GuidedTour.CM.create.content": "<p>T<PERSON><PERSON> và quản lý tất cả nội dung ở đây trong Trình quản lý nội dung.</p><p>Ví dụ: L<PERSON>y ví dụ về trang web Blog hơn nữa, người ta có thể viết một B<PERSON><PERSON> báo, lưu và xuất bản nó theo ý muốn.</p><p>💡 Mẹo nhanh — Đừng quên nhấn xuất bản nội dung bạn tạo.</p>", "app.components.GuidedTour.CM.create.title": "⚡️ <PERSON>áng tạo nội dung", "app.components.GuidedTour.CM.success.content": "<p><PERSON><PERSON><PERSON><PERSON> vời, còn một bước cuối cùng nữa!</p><b>🚀 Xem nội dung đang hoạt động</b>", "app.components.GuidedTour.CM.success.cta.title": "<PERSON><PERSON><PERSON> tra <PERSON>", "app.components.GuidedTour.CM.success.title": "Bước 2: <PERSON><PERSON><PERSON> thành ✅", "app.components.GuidedTour.CTB.create.content": "<p><PERSON><PERSON><PERSON> bộ sưu tập gi<PERSON><PERSON> bạn quản lý nhiều mục nhập, <PERSON><PERSON><PERSON> đơn lẻ phù hợp để chỉ quản lý một mục nhập.</p> <p>Ví dụ: Đ<PERSON><PERSON> với trang web Blog, <PERSON>à<PERSON> viết sẽ là Loại Bộ sưu tập trong khi Trang chủ sẽ là Loại Đơn.</p>", "app.components.GuidedTour.CTB.create.cta.title": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> lo<PERSON> bộ sưu tập", "app.components.GuidedTour.CTB.create.title": "🧠 <PERSON><PERSON><PERSON>ộ sưu tập đầu tiên", "app.components.GuidedTour.CTB.success.content": "<p><PERSON><PERSON><PERSON> lắm!</p><b>⚡️ Bạn muốn chia sẻ điều gì với thế giới?</b>", "app.components.GuidedTour.CTB.success.title": "Bước 1: <PERSON><PERSON><PERSON> thành ✅", "app.components.GuidedTour.apiTokens.create.content": "<p>T<PERSON><PERSON> mã thông báo xác thực tại đây và truy xuất nội dung bạn vừa tạo.</p>", "app.components.GuidedTour.apiTokens.create.cta.title": "Tạo API Token", "app.components.GuidedTour.apiTokens.create.title": "🚀 Xem nội dung đang hoạt động", "app.components.GuidedTour.apiTokens.success.content": "<p>Xem nội dung đang hoạt động bằng cách thực hiện yêu cầu HTTP:</p><ul><li><p>Tới URL này: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>Với tiêu đề: <light>Ủy quyền: người mang '<'YOUR_API_TOKEN'>'</light></p></li></ul><p><PERSON><PERSON> biết thêm các cách tương tác với nội dung, hãy xem <documentationLink>tài liệu</documentationLink>.</p>", "app.components.GuidedTour.apiTokens.success.cta.title": "Quay lại trang chủ", "app.components.GuidedTour.apiTokens.success.title": "Bước 3: <PERSON><PERSON><PERSON> thành ✅", "app.components.GuidedTour.create-content": "<PERSON><PERSON><PERSON> n<PERSON>i dung", "app.components.GuidedTour.home.CM.title": "⚡️ Bạn muốn chia sẻ điều gì với thế giới?", "app.components.GuidedTour.home.CTB.cta.title": "<PERSON><PERSON><PERSON><PERSON> đến Trình tạo loại nội dung", "app.components.GuidedTour.home.CTB.title": "🧠 <PERSON><PERSON><PERSON> dựng cấu trúc nội dung", "app.components.GuidedTour.home.apiTokens.cta.title": "<PERSON><PERSON><PERSON> tra <PERSON>", "app.components.GuidedTour.skip": "Bỏ qua chuyến tham quan", "app.components.GuidedTour.title": "3 bư<PERSON><PERSON> để b<PERSON><PERSON> đầu", "app.components.HomePage.button.blog": "XEM THÊM TRÊN BLOG", "app.components.HomePage.community": "T<PERSON><PERSON> cộng đồng trên web", "app.components.HomePage.community.content": "<PERSON><PERSON><PERSON><PERSON> luận với thành viên, ng<PERSON><PERSON><PERSON> đóng góp và lập trình viên trên các kênh khác nhau.", "app.components.HomePage.create": "<PERSON><PERSON><PERSON> loại nội dung đầu tiên của bạn", "app.components.HomePage.roadmap": "<PERSON><PERSON> lộ trình của chúng tôi", "app.components.HomePage.welcome": "<PERSON><PERSON>o mừng bạn đã tham gia 👋", "app.components.HomePage.welcome.again": "<PERSON><PERSON>o mừng 👋", "app.components.HomePage.welcomeBlock.content": "<PERSON><PERSON><PERSON> tôi hạnh phúc khi bạn là một phần của cộng đồng.  ", "app.components.HomePage.welcomeBlock.content.again": "<PERSON>úng tôi hi vọng bạn có tiến trình phát triển trên dự án của bạn... Bạn tự động đọc tin tức mới nhất về Strapi. ", "app.components.HomePage.welcomeBlock.content.issues": "vấn đề.", "app.components.HomePage.welcomeBlock.content.raise": " hoặc nêu lên ", "app.components.ImgPreview.hint": "<PERSON><PERSON><PERSON> và thả tệp của bạn vào khu vực này hoặc {browse} để tìm tệp tải lên", "app.components.ImgPreview.hint.browse": "<PERSON><PERSON><PERSON><PERSON>", "app.components.InputFile.newFile": "<PERSON><PERSON><PERSON><PERSON> tập tin mới", "app.components.InputFileDetails.open": "Mở ra ở thẻ mới", "app.components.InputFileDetails.originalName": "<PERSON><PERSON><PERSON> g<PERSON>:", "app.components.InputFileDetails.remove": "<PERSON><PERSON><PERSON> tập tin này", "app.components.InputFileDetails.size": "<PERSON><PERSON><PERSON>:", "app.components.InstallPluginPage.Download.description": "Sẽ mất một vài giây để tải xuống và cài đặt plugin.", "app.components.InstallPluginPage.Download.title": "<PERSON><PERSON> tải xu<PERSON>...", "app.components.InstallPluginPage.description": "<PERSON><PERSON> dàng mở rộng ứng dụng của bạn", "app.components.LeftMenu.collapse": "<PERSON><PERSON> gọn thanh điều hư<PERSON>ng", "app.components.LeftMenu.expand": "Mở rộng thanh điều hướng", "app.components.LeftMenu.general": "<PERSON><PERSON><PERSON> quan", "app.components.LeftMenu.logo.alt": "<PERSON><PERSON><PERSON><PERSON> tư<PERSON>ng dụng", "app.components.LeftMenu.logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "app.components.LeftMenu.navbrand.title": "<PERSON><PERSON><PERSON> điều khiển <PERSON>rap<PERSON>", "app.components.LeftMenu.navbrand.workplace": "<PERSON><PERSON><PERSON> làm vi<PERSON>c", "app.components.LeftMenu.plugins": "b<PERSON> sung", "app.components.LeftMenuFooter.help": "<PERSON><PERSON><PERSON> g<PERSON>", "app.components.LeftMenuFooter.poweredBy": "<PERSON>ung cấp bởi ", "app.components.LeftMenuLinkContainer.collectionTypes": "<PERSON><PERSON><PERSON> bộ s<PERSON>u tập", "app.components.LeftMenuLinkContainer.configuration": "<PERSON><PERSON><PERSON> h<PERSON>nh", "app.components.LeftMenuLinkContainer.general": "<PERSON>", "app.components.LeftMenuLinkContainer.noPluginsInstalled": "Chưa có plugin nào đư<PERSON>c cài đặt", "app.components.LeftMenuLinkContainer.plugins": "b<PERSON> sung", "app.components.LeftMenuLinkContainer.singleTypes": "<PERSON><PERSON><PERSON> đ<PERSON>n", "app.components.ListPluginsPage.deletePlugin.description": "<PERSON><PERSON> thể mất vài giây để gỡ cài đặt plugin.", "app.components.ListPluginsPage.deletePlugin.title": "Gỡ cài đặt", "app.components.ListPluginsPage.description": "<PERSON><PERSON> s<PERSON>ch các plugin đã được cài đặt trong dự án.", "app.components.ListPluginsPage.helmet.title": "<PERSON><PERSON> s<PERSON>ch plugin", "app.components.Logout.logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "app.components.Logout.profile": "<PERSON><PERSON> sơ", "app.components.MarketplaceBanner": "Khám phá các plugin do cộng đồng xây dựng và nhiều điều tuyệt vời khác để khởi động dự án của bạn trên Strapi Marketplace.", "app.components.MarketplaceBanner.image.alt": "<PERSON>go tên l<PERSON><PERSON>", "app.components.MarketplaceBanner.link": "<PERSON><PERSON><PERSON> tra nó ngay", "app.components.NotFoundPage.back": "Trở về trang chủ", "app.components.NotFoundPage.description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy", "app.components.Official": "<PERSON><PERSON><PERSON>", "app.components.Onboarding.help.button": "Nút trợ gi<PERSON>p", "app.components.Onboarding.label.completed": "% đã hoàn thành", "app.components.Onboarding.link.build-content": "<PERSON><PERSON><PERSON> dựng kiến ​​trúc nội dung", "app.components.Onboarding.link.manage-content": "<PERSON><PERSON><PERSON><PERSON> vào", "app.components.Onboarding.link.manage-media": "<PERSON><PERSON><PERSON><PERSON> lý p<PERSON><PERSON><PERSON> tiện", "app.components.Onboarding.link.more-videos": "<PERSON><PERSON>ê<PERSON> video", "app.components.Onboarding.title": "Videos B<PERSON><PERSON>", "app.components.PluginCard.Button.label.download": "Load về", "app.components.PluginCard.Button.label.install": "Đã cài đặt", "app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "<PERSON><PERSON><PERSON> n<PERSON>ng autoReload không hoạt động. ", "app.components.PluginCard.PopUpWarning.install.impossible.confirm": "Tôi hiểu rồi!", "app.components.PluginCard.PopUpWarning.install.impossible.environment": "<PERSON>ì lý do bả<PERSON> mậ<PERSON> n<PERSON>, chỉ có một plugin có thể được tải xuống trong môi trường phát triển môi trường.", "app.components.PluginCard.PopUpWarning.install.impossible.title": "<PERSON>ả<PERSON> về không khả thi", "app.components.PluginCard.compatible": "Tương tự với ứng dụng của bạn", "app.components.PluginCard.compatibleCommunity": "Tương tự với cộng đồng", "app.components.PluginCard.more-details": "<PERSON><PERSON><PERSON><PERSON> chi tiết", "app.components.ToggleCheckbox.off-label": "SAI", "app.components.ToggleCheckbox.on-label": "ĐÚNG", "app.components.Users.MagicLink.connect": "<PERSON>o chép và chia sẻ liên kết này để cấp quyền truy cập cho người dùng này", "app.components.Users.MagicLink.connect.sso": "<PERSON><PERSON><PERSON> liên kết này cho ngư<PERSON>i dùng, lầ<PERSON> đăng nhập đầu tiên có thể được thực hiện thông qua nhà cung cấp SSO", "app.components.Users.ModalCreateBody.block-title.details": "<PERSON> tiết người dùng", "app.components.Users.ModalCreateBody.block-title.roles": "<PERSON>ai trò của người dùng", "app.components.Users.ModalCreateBody.block-title.roles.description": "Một người dùng có thể có một hoặc nhiều vai trò", "app.components.Users.SortPicker.button-label": "<PERSON><PERSON><PERSON> xếp theo", "app.components.Users.SortPicker.sortby.email_asc": "<PERSON>ail (A đến Z)", "app.components.Users.SortPicker.sortby.email_desc": "Email (Z đến A)", "app.components.Users.SortPicker.sortby.firstname_asc": "<PERSON><PERSON><PERSON> (A đến Z)", "app.components.Users.SortPicker.sortby.firstname_desc": "<PERSON><PERSON><PERSON> (Z đến A)", "app.components.Users.SortPicker.sortby.lastname_asc": "<PERSON><PERSON> (A đến Z)", "app.components.Users.SortPicker.sortby.lastname_desc": "<PERSON><PERSON> (Z đến A)", "app.components.Users.SortPicker.sortby.username_asc": "<PERSON><PERSON><PERSON><PERSON> dù<PERSON> (A đến Z)", "app.components.Users.SortPicker.sortby.username_desc": "<PERSON><PERSON><PERSON><PERSON> dù<PERSON> (Z đến A)", "app.components.listPlugins.button": "Thêm plugin mới", "app.components.listPlugins.title.none": "Chưa có plugin nào đư<PERSON>c cài đặt", "app.components.listPluginsPage.deletePlugin.error": "<PERSON><PERSON><PERSON> ra lỗi khi gỡ bỏ plugin", "app.containers.App.notification.error.init": "<PERSON><PERSON> xảy ra lỗi khi gửi yêu cầu đến API", "app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin": "<PERSON><PERSON><PERSON> bạn không nhận đư<PERSON><PERSON> liên kết nà<PERSON>, vui lòng liên hệ với quản trị viên của bạn.", "app.containers.AuthPage.ForgotPasswordSuccess.text.email": "<PERSON><PERSON> thể mất vài phút để nhận đượ<PERSON> liên kết khôi phục mật khẩu của bạn.", "app.containers.AuthPage.ForgotPasswordSuccess.title": "Đ<PERSON> gửi email", "app.containers.Users.EditPage.form.active.label": "<PERSON><PERSON><PERSON> đ<PERSON>", "app.containers.Users.EditPage.header.label": "Chỉnh sửa thông tin", "app.containers.Users.EditPage.header.label-loading": "<PERSON><PERSON><PERSON><PERSON> dùng biên tập", "app.containers.Users.EditPage.roles-bloc-title": "<PERSON><PERSON> trò đ<PERSON><PERSON><PERSON> phân bổ", "app.containers.Users.ModalForm.footer.button-success": "<PERSON><PERSON><PERSON> người dùng", "app.links.configure-view": "<PERSON><PERSON><PERSON> cấu hình chế độ xem", "app.page.not.found": "Ối! ", "app.static.links.cheatsheet": "CheatSheet", "app.utils.SelectOption.defaultMessage": " ", "app.utils.add-filter": "<PERSON><PERSON><PERSON><PERSON> bộ lọc", "app.utils.close-label": "Đ<PERSON><PERSON>", "app.utils.defaultMessage": " ", "app.utils.delete": "Xóa bỏ", "app.utils.duplicate": "<PERSON><PERSON><PERSON> b<PERSON>n", "app.utils.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.errors.file-too-big.message": "<PERSON><PERSON><PERSON> quá lớn", "app.utils.filter-value": "<PERSON><PERSON><PERSON> trị bộ lọc", "app.utils.filters": "<PERSON><PERSON> lọc", "app.utils.notify.data-loaded": "{target} đ<PERSON> đ<PERSON><PERSON><PERSON> t<PERSON>i", "app.utils.placeholder.defaultMessage": " ", "app.utils.publish": "<PERSON><PERSON><PERSON> b<PERSON>", "app.utils.refresh": "<PERSON><PERSON><PERSON> cho khỏe lại", "app.utils.select-all": "<PERSON><PERSON><PERSON> tất cả", "app.utils.select-field": "<PERSON><PERSON><PERSON> tr<PERSON>", "app.utils.select-filter": "<PERSON><PERSON><PERSON> bộ lọc", "app.utils.unpublish": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>n", "app.utils.published": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> h<PERSON>nh", "app.utils.ready-to-publish": "Sẵn sàng xu<PERSON>t bản", "clearLabel": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>g", "coming.soon": "Nội dung này hiện đang được xây dựng và sẽ hoạt động trở lại sau vài tuần nữa!", "component.Input.error.validation.integer": "<PERSON><PERSON><PERSON> trị phải là số nguyên", "components.AutoReloadBlocker.description": "Chạy Strapi với một trong các lệnh sau:", "components.AutoReloadBlocker.header": "<PERSON><PERSON><PERSON> n<PERSON>ng <PERSON>ải lại bị bắt buộc cho plugin này.", "components.ErrorBoundary.title": "<PERSON><PERSON><PERSON><PERSON> gì đó k<PERSON>...", "components.FilterOptions.FILTER_TYPES.$contains": "ch<PERSON><PERSON>", "components.FilterOptions.FILTER_TYPES.$containsi": "chứa (không phân biệt chữ hoa chữ thường)", "components.FilterOptions.FILTER_TYPES.$endsWith": "kết thúc bằng", "components.FilterOptions.FILTER_TYPES.$endsWithi": "kết thúc bằng (không phân biệt chữ hoa chữ thường)", "components.FilterOptions.FILTER_TYPES.$eq": "là", "components.FilterOptions.FILTER_TYPES.$eqi": "là (không phân biệt chữ hoa chữ thường)", "components.FilterOptions.FILTER_TYPES.$gt": "l<PERSON><PERSON> h<PERSON>n", "components.FilterOptions.FILTER_TYPES.$gte": "là lớn hơn hoặc bằng", "components.FilterOptions.FILTER_TYPES.$lt": "thấ<PERSON> h<PERSON>n", "components.FilterOptions.FILTER_TYPES.$lte": "thấp hơn hoặc bằng", "components.FilterOptions.FILTER_TYPES.$ne": "kh<PERSON>ng phải", "components.FilterOptions.FILTER_TYPES.$nei": "không phải (không phân biệt chữ hoa chữ thường)", "components.FilterOptions.FILTER_TYPES.$notContains": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>a", "components.FilterOptions.FILTER_TYPES.$notContainsi": "không chứa (không phân biệt chữ hoa chữ thường)", "components.FilterOptions.FILTER_TYPES.$notNull": "không phải là rỗng", "components.FilterOptions.FILTER_TYPES.$null": "là vô giá trị", "components.FilterOptions.FILTER_TYPES.$startsWith": "b<PERSON>t đ<PERSON>u với", "components.FilterOptions.FILTER_TYPES.$startsWithi": "bắt đầu bằng (không phân biệt chữ hoa chữ thường)", "components.Input.error.attribute.key.taken": "<PERSON><PERSON>á trị này đã tồn tại", "components.Input.error.attribute.sameKeyAndName": "<PERSON><PERSON><PERSON><PERSON> thể kết hợp với nhau", "components.Input.error.attribute.taken": "This input đã tồn tại", "components.Input.error.contain.lowercase": "<PERSON><PERSON>t khẩu phải chứa ít nhất một ký tự chữ thường", "components.Input.error.contain.number": "<PERSON><PERSON><PERSON> khẩu phải chứa ít nhất một chữ số", "components.Input.error.contain.uppercase": "<PERSON><PERSON>t khẩu phải chứa ít nhất một ký tự viết hoa", "components.Input.error.contentTypeName.taken": "Tên này đã tồn tại", "components.Input.error.custom-error": "{errorMessage} ", "components.Input.error.password.noMatch": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "components.Input.error.validation.email": "<PERSON><PERSON> h<PERSON> l<PERSON>", "components.Input.error.validation.json": "This is not a JSON format", "components.Input.error.validation.lowercase": "<PERSON><PERSON><PERSON> trị phải là một chuỗi chữ thường", "components.Input.error.validation.max": "<PERSON><PERSON><PERSON> trị quá cao {max}.", "components.Input.error.validation.maxLength": "<PERSON><PERSON><PERSON> trị quá dài {max}.", "components.Input.error.validation.min": "<PERSON><PERSON><PERSON> trị quá thấp {min}.", "components.Input.error.validation.minLength": "<PERSON><PERSON><PERSON> trị quá ngắn {min}.", "components.Input.error.validation.minSupMax": "Can't over exot", "components.Input.error.validation.regex": "<PERSON><PERSON><PERSON> trị không đ<PERSON><PERSON><PERSON> cập nhật với biểu thức ch<PERSON>h quy.", "components.Input.error.validation.required": "<PERSON><PERSON><PERSON> tr<PERSON> này b<PERSON> bu<PERSON>.", "components.Input.error.validation.unique": "<PERSON><PERSON><PERSON> trị này đã được sử dụng.", "components.Input.error.validation.email.withField": "{field} là email không hợp lệ", "components.Input.error.validation.json.withField": "{field} kh<PERSON>ng khớp với định dạng JSON", "components.Input.error.validation.lowercase.withField": "{field} phải là chuỗi chữ thường", "components.Input.error.validation.max.withField": "{field} quá cao.", "components.Input.error.validation.maxLength.withField": "{field} quá dài.", "components.Input.error.validation.min.withField": "{field} quá thấp.", "components.Input.error.validation.minLength.withField": "{field} qu<PERSON>.", "components.Input.error.validation.minSupMax.withField": "{field} kh<PERSON><PERSON> thể v<PERSON><PERSON><PERSON> tr<PERSON><PERSON> hơn", "components.Input.error.validation.regex.withField": "{field} kh<PERSON>ng khớp với biểu thức ch<PERSON>h quy.", "components.Input.error.validation.required.withField": "{field} is required.", "components.Input.error.validation.unique.withField": "{field} đ<PERSON> đ<PERSON><PERSON>c sử dụng.", "components.InputSelect.option.placeholder": "Chọn ở đây", "components.ListRow.empty": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu để hiển thị.", "components.NotAllowedInput.text": "<PERSON><PERSON><PERSON><PERSON> có quyền xem trường này", "components.OverlayBlocker.description": "Bạn đang sử dụng một tính năng cần khởi động lại máy chủ. ", "components.OverlayBlocker.description.serverError": "<PERSON><PERSON><PERSON> chủ nên được khởi động lại, vui lòng kiểm tra nhật ký của bạn trong lệnh cửa sổ", "components.OverlayBlocker.title": "<PERSON>ang đợi khởi động lại...", "components.OverlayBlocker.title.serverError": "Việc khởi động lại tốn nhiều thời gian hơn mong đợi", "components.PageFooter.select": "bản ghi trên trang", "components.ProductionBlocker.description": "Vì lý do nào chúng tôi phải vô hiệu hóa plugin trong các môi trường khác", "components.ProductionBlocker.header": "Plugin này chỉ hiệu quả trong môi trường phát triển", "components.Search.placeholder": "<PERSON><PERSON><PERSON> k<PERSON>...", "components.TableHeader.sort": "<PERSON><PERSON><PERSON> xếp theo {label}", "components.Wysiwyg.ToggleMode.markdown-mode": "Markdown mode", "components.Wysiwyg.ToggleMode.preview-mode": "<PERSON><PERSON> độ xem trước", "components.Wysiwyg.collapse": "<PERSON><PERSON> l<PERSON>i", "components.Wysiwyg.selectOptions.H1": "Ti<PERSON><PERSON> đề H1", "components.Wysiwyg.selectOptions.H2": "Ti<PERSON>u đề H2", "components.Wysiwyg.selectOptions.H3": "Ti<PERSON><PERSON> đề H3", "components.Wysiwyg.selectOptions.H4": "Ti<PERSON><PERSON> đề H4", "components.Wysiwyg.selectOptions.H5": "<PERSON>i<PERSON><PERSON> đề H5", "components.Wysiwyg.selectOptions.H6": "<PERSON><PERSON><PERSON><PERSON> đề H6", "components.Wysiwyg.selectOptions.title": "<PERSON><PERSON><PERSON><PERSON> một tiêu đề", "components.WysiwygBottomControls.charactersIndicators": "ký tự", "components.WysiwygBottomControls.fullscreen": "Mở rộng", "components.WysiwygBottomControls.uploadFiles": "<PERSON><PERSON><PERSON> và thả các tập tin, dán từ bộ nhớ tạm thời hoặc {duyệt}.", "components.WysiwygBottomControls.uploadFiles.browse": "<PERSON><PERSON><PERSON> t<PERSON>", "components.pagination.go-to": "<PERSON><PERSON><PERSON> trang {page}", "components.pagination.go-to-next": "<PERSON><PERSON><PERSON><PERSON> đến trang tiếp theo", "components.pagination.go-to-previous": "<PERSON><PERSON> tới trang trước", "components.pagination.remaining-links": "Và {number} liên kết kh<PERSON>c", "components.popUpWarning.button.cancel": "Không, hủy", "components.popUpWarning.button.confirm": "<PERSON><PERSON><PERSON>", "components.popUpWarning.message": "Bạn có chắc chắn muốn xóa nó không?", "components.popUpWarning.title": "<PERSON><PERSON> lòng x<PERSON>n", "content-manager.App.schemas.data-loaded": "<PERSON><PERSON><PERSON> lư<PERSON><PERSON> đồ đã đư<PERSON><PERSON> tải thành công", "content-manager.ListViewTable.relation-loaded": "<PERSON><PERSON><PERSON> quan hệ đã đ<PERSON><PERSON><PERSON> tải", "content-manager.ListViewTable.relation-loading": "<PERSON><PERSON><PERSON> quan hệ đang tải", "content-manager.ListViewTable.relation-more": "<PERSON><PERSON><PERSON> quan hệ này chứa nhiều thực thể hơn số lượng đư<PERSON><PERSON> hiển thị", "content-manager.EditRelations.title": "<PERSON><PERSON> thống quản lý dữ liệu", "content-manager.HeaderLayout.button.label-add-entry": "<PERSON><PERSON><PERSON> mục mới", "content-manager.api.id": "Mã API", "content-manager.apiError.This attribute must be unique": "{field} ph<PERSON>i là duy nhất", "content-manager.components.AddFilterCTA.add": "<PERSON><PERSON><PERSON>", "content-manager.components.AddFilterCTA.hide": "<PERSON><PERSON><PERSON>", "content-manager.components.DragHandle-label": "Drag", "content-manager.components.DraggableAttr.edit": "Click để chỉnh sửa", "content-manager.components.DraggableCard.delete.field": "Xóa {item}", "content-manager.components.DraggableCard.edit.field": "Chỉnh sửa {item}", "content-manager.components.DraggableCard.move.field": "<PERSON> chuyển {item}", "content-manager.components.ListViewTable.row-line": "dòng mục {number}", "content-manager.components.DynamicZone.ComponentPicker-label": "<PERSON><PERSON><PERSON> một thành phần", "content-manager.components.DynamicZone.add-component": "<PERSON><PERSON><PERSON><PERSON> thành phần vào {componentName}", "content-manager.components.DynamicZone.delete-label": "<PERSON><PERSON><PERSON> {name}", "content-manager.components.DynamicZone.error-message": "<PERSON><PERSON><PERSON><PERSON> phần nà<PERSON> (các) lỗi", "content-manager.components.DynamicZone.missing-components": "There {number, plural, =0 {are # missing components} one {is # missing component} other {are # missing components}}", "content-manager.components.DynamicZone.move-down-label": "<PERSON> chuyển thành phần xuống", "content-manager.components.DynamicZone.move-up-label": "<PERSON> chuyển thành phần lên", "content-manager.components.DynamicZone.pick-compo": "<PERSON><PERSON><PERSON> một thành phần", "content-manager.components.DynamicZone.required": "<PERSON><PERSON><PERSON><PERSON> phần là bắt buộc", "content-manager.components.EmptyAttributesBlock.button": "Đến trang cài đặt", "content-manager.components.EmptyAttributesBlock.description": "Bạn có thể thay đổi cài đặt của mình", "content-manager.components.FieldItem.linkToComponentLayout": "Đặt b<PERSON> cục của thành phần", "content-manager.components.FieldSelect.label": "<PERSON><PERSON><PERSON><PERSON> một trường", "content-manager.components.FilterOptions.button.apply": "<PERSON><PERSON>", "content-manager.components.FiltersPickWrapper.PluginHeader.actions.apply": "<PERSON><PERSON>", "content-manager.components.FiltersPickWrapper.PluginHeader.actions.clearAll": "<PERSON><PERSON><PERSON> tất cả", "content-manager.components.FiltersPickWrapper.PluginHeader.description": "<PERSON>ài đặt các điều kiện để áp dụng cho việc lọc các bản ghi", "content-manager.components.FiltersPickWrapper.PluginHeader.title.filter": "<PERSON><PERSON><PERSON> bộ lọc", "content-manager.components.FiltersPickWrapper.hide": "Ẩn đi", "content-manager.components.LeftMenu.Search.label": "<PERSON><PERSON><PERSON> kiếm loại nội dung", "content-manager.components.LeftMenu.collection-types": "Collection Types", "content-manager.components.LeftMenu.single-types": "Single Types", "content-manager.components.LimitSelect.itemsPerPage": "<PERSON><PERSON> l<PERSON> bản ghi trong trang", "content-manager.components.NotAllowedInput.text": "<PERSON><PERSON><PERSON><PERSON> có quyền xem trường này", "content-manager.components.RelationInput.icon-button-aria-label": "Drag", "content-manager.components.RepeatableComponent.error-message": "The component(s) contain error(s)", "content-manager.components.Search.placeholder": "<PERSON><PERSON><PERSON> m<PERSON>t bản ghi...", "content-manager.components.Select.draft-info-title": "Trạng thái: <PERSON><PERSON><PERSON><PERSON>", "content-manager.components.Select.publish-info-title": "Trạng thái: <PERSON><PERSON> đăng", "content-manager.components.SettingsViewWrapper.pluginHeader.description.edit-settings": "<PERSON><PERSON><PERSON> chỉnh giao diện chỉnh sửa sẽ trông như thế nào.", "content-manager.components.SettingsViewWrapper.pluginHeader.description.list-settings": "<PERSON><PERSON><PERSON> định cài đặt của chế độ xem danh sách.", "content-manager.components.SettingsViewWrapper.pluginHeader.title": "<PERSON><PERSON><PERSON> cấu hình chế độ xem — {name}", "content-manager.bulk-publish.already-published": "<PERSON><PERSON> đăng", "content-manager.components.TableDelete.delete": "<PERSON><PERSON><PERSON> tất cả", "content-manager.components.TableDelete.deleteSelected": "Xóa lựa chọn đã chọn", "content-manager.components.TableDelete.label": "{number, plural, one {# entry} other {# entries}} selected", "content-manager.components.TableEmpty.withFilters": "<PERSON><PERSON><PERSON><PERSON> có {contentType} để sử dụng bộ lọc", "content-manager.components.TableEmpty.withSearch": "<PERSON><PERSON><PERSON><PERSON> có {contentType} phù hợp với tìm kiếm ({search})...", "content-manager.components.TableEmpty.withoutFilter": "<PERSON><PERSON><PERSON><PERSON> có {contentType}...", "content-manager.components.empty-repeatable": "<PERSON><PERSON>a có mục nào. ", "content-manager.components.notification.info.maximum-requirement": "Bạn đã đạt đến số lượng trường tối đa", "content-manager.components.notification.info.minimum-requirement": "<PERSON>ột trường đã đư<PERSON><PERSON> thêm vào để đáp ứng yêu cầu tối thiểu", "content-manager.components.repeatable.reorder.error": "<PERSON><PERSON> xảy ra lỗi khi sắp xếp lại trường thành phần của bạn, vui lòng thử lại", "content-manager.components.reset-entry": "Reset entry", "content-manager.components.uid.apply": "<PERSON><PERSON> d<PERSON>", "content-manager.components.uid.available": "<PERSON><PERSON> sẵn", "content-manager.components.uid.regenerate": "<PERSON><PERSON><PERSON> lạ<PERSON>", "content-manager.components.uid.suggested": "<PERSON><PERSON> xuất", "content-manager.components.uid.unavailable": "<PERSON><PERSON><PERSON>ng có sẵn", "content-manager.containers.Edit.Link.Layout": "<PERSON><PERSON><PERSON> hình b<PERSON> cục", "content-manager.containers.Edit.Link.Model": "Chỉnh sửa Collection-Type", "content-manager.containers.Edit.addAnItem": "<PERSON><PERSON><PERSON><PERSON> một bản ghi...", "content-manager.containers.Edit.clickToJump": "<PERSON>hấn để nh<PERSON>y vào bản ghi", "content-manager.containers.Edit.delete": "Xóa", "content-manager.containers.Edit.delete-entry": "<PERSON><PERSON><PERSON> m<PERSON> n<PERSON>", "content-manager.containers.Edit.editing": "<PERSON><PERSON> sửa...", "content-manager.containers.Edit.information": "Thông tin", "content-manager.containers.Edit.information.by": "By", "content-manager.containers.Edit.information.created": "Đã tạo", "content-manager.containers.Edit.information.draftVersion": "b<PERSON><PERSON>", "content-manager.containers.Edit.information.editing": "Chỉnh sửa", "content-manager.containers.Edit.information.lastUpdate": "<PERSON><PERSON><PERSON> nh<PERSON>t cu<PERSON>i cùng", "content-manager.containers.Edit.information.publishedVersion": "p<PERSON><PERSON>n bản đã đăng", "content-manager.containers.Edit.pluginHeader.title.new": "<PERSON><PERSON><PERSON> một bản ghi", "content-manager.containers.Edit.reset": "<PERSON><PERSON><PERSON>", "content-manager.containers.Edit.returnList": "Trở về danh sách", "content-manager.containers.Edit.seeDetails": "<PERSON> ti<PERSON>", "content-manager.containers.Edit.submit": "<PERSON><PERSON><PERSON>", "content-manager.containers.EditSettingsView.modal-form.edit-field": "Chỉnh sửa trường", "content-manager.containers.EditView.add.new-entry": "<PERSON><PERSON><PERSON><PERSON> một mục", "content-manager.containers.EditView.notification.errors": "<PERSON><PERSON><PERSON> nhập liệu có vài lỗi", "content-manager.containers.Home.introduction": "Để chỉnh sửa bản ghi của bạn, hãy đi đến liên kết ở menu bên trái. ", "content-manager.containers.Home.pluginHeaderDescription": "<PERSON><PERSON><PERSON><PERSON> lý các bản ghi thông qua một giao diện mạnh và đẹp.", "content-manager.containers.Home.pluginHeaderTitle": "<PERSON><PERSON><PERSON><PERSON>", "content-manager.containers.List.draft": "<PERSON><PERSON><PERSON>", "content-manager.containers.List.errorFetchRecords": "Lỗi", "content-manager.containers.List.published": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> h<PERSON>nh", "content-manager.containers.ListPage.displayedFields": "<PERSON><PERSON><PERSON> trường đã đư<PERSON><PERSON> trình bày", "content-manager.containers.ListPage.items": "{number, plural, =0 {items} one {item} other {items}}", "content-manager.containers.ListPage.table-headers.publishedAt": "<PERSON><PERSON><PERSON><PERSON> thái", "content-manager.containers.ListPage.table-headers.title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "content-manager.containers.ListPage.table-headers.description": "<PERSON><PERSON>", "content-manager.containers.ListPage.table-headers.createdAt": "<PERSON><PERSON><PERSON>", "content-manager.containers.ListPage.table-headers.updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>", "content-manager.containers.ListPage.table-headers.name": "<PERSON><PERSON><PERSON>", "content-manager.containers.ListPage.table-headers.price": "Giá", "content-manager.containers.ListPage.table-headers.discount": "G<PERSON>ảm giá", "content-manager.containers.ListPage.table-headers.phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "content-manager.containers.ListPage.selectedEntriesModal.title": "<PERSON><PERSON><PERSON> b<PERSON>n c<PERSON>c mục", "content-manager.containers.ListPage.selectedEntriesModal.selectedCount": "<b>{alreadyPublishedCount}</b> {alreadyPublishedCount, plural, =0 {entries} one {entry} other {entries}} already published. <b>{readyToPublishCount}</b> {readyToPublishCount, plural, =0 {entries} one {entry} other {entries}} ready to publish. <b>{withErrorsCount}</b> {withErrorsCount, plural, =0 {entries} one {entry} other {entries}} waiting for action.", "content-manager.containers.ListPage.selectedEntriesModal.publishedCount": "<b>{publishedCount}</b> {publishedCount, plural, =0 {entries} one {entry} other {entries}} published. <b>{withErrorsCount}</b> {withErrorsCount, plural, =0 {entries} one {entry} other {entries}} waiting for action.", "content-manager.containers.ListSettingsView.modal-form.edit-label": "Chỉnh sửa {fieldName}", "content-manager.containers.SettingPage.add.field": "<PERSON><PERSON><PERSON> m<PERSON>t trư<PERSON><PERSON> k<PERSON>c", "content-manager.containers.SettingPage.add.relational-field": "<PERSON><PERSON><PERSON> một trường liên quan khác", "content-manager.containers.SettingPage.attributes": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> thu<PERSON><PERSON> t<PERSON>h", "content-manager.containers.SettingPage.attributes.description": "<PERSON><PERSON><PERSON> ngh<PERSON>a thứ tự các thuộc t<PERSON>h", "content-manager.containers.SettingPage.editSettings.description": "Kéo & thả các trường để xây dựng bố cục", "content-manager.containers.SettingPage.editSettings.entry.title": "<PERSON><PERSON><PERSON> b<PERSON>hi", "content-manager.containers.SettingPage.editSettings.entry.title.description": "Cài đặt trường được trình bày trong bản ghi của bạn", "content-manager.containers.SettingPage.editSettings.relation-field.description": "Đặt trường được hiển thị trong cả chế độ xem chỉnh sửa và danh sách", "content-manager.containers.SettingPage.editSettings.title": "Chỉnh sửa hiển thị (các cài đặt)", "content-manager.containers.SettingPage.layout": "Bố cục", "content-manager.containers.SettingPage.listSettings.description": "<PERSON><PERSON><PERSON> cấu hình các tùy chọn cho <PERSON> bộ sưu tập này", "content-manager.containers.SettingPage.listSettings.title": "<PERSON><PERSON><PERSON> thị danh s<PERSON>ch (các cài đặt)", "content-manager.containers.SettingPage.pluginHeaderDescription": "<PERSON><PERSON><PERSON> cấu hình cài đặt cụ thể cho Lo<PERSON> bộ sưu tập này", "content-manager.containers.SettingPage.relations": "<PERSON><PERSON><PERSON> field liên quan", "content-manager.containers.SettingPage.settings": "<PERSON><PERSON><PERSON> c<PERSON> đặt", "content-manager.containers.SettingPage.view": "Xem", "content-manager.containers.SettingViewModel.pluginHeader.title": "<PERSON><PERSON><PERSON><PERSON> - {name}", "content-manager.containers.SettingsPage.Block.contentType.description": "<PERSON><PERSON><PERSON> hình cài đặt riêng", "content-manager.containers.SettingsPage.Block.contentType.title": "<PERSON><PERSON><PERSON> bộ s<PERSON>u tập", "content-manager.containers.SettingsPage.Block.generalSettings.description": "<PERSON><PERSON><PERSON> cấu hình các tùy chọn mặc định cho <PERSON> bộ sưu tập của bạn", "content-manager.containers.SettingsPage.Block.generalSettings.title": "<PERSON>", "content-manager.containers.SettingsPage.pluginHeaderDescription": "<PERSON><PERSON><PERSON> cấu hình cài đặt cho tất cả các <PERSON> và Nhóm Bộ sưu tập của bạn", "content-manager.containers.SettingsView.list.subtitle": "<PERSON><PERSON><PERSON> cấu hình bố cục và hiển thị các <PERSON> và Nhóm Bộ sưu tập của bạn", "content-manager.containers.SettingsView.list.title": "<PERSON><PERSON><PERSON> cấu hình đư<PERSON>c trình bày", "content-manager.dnd.cancel-item": "{item}, dropped. Re-order cancelled.", "content-manager.dnd.drop-item": "{item}, dropped. Final position in list: {position}.", "content-manager.dnd.grab-item": "{item}, grabbed. Current position in list: {position}. Press up and down arrow to change position, Spacebar to drop, Escape to cancel.", "content-manager.dnd.instructions": "<PERSON><PERSON><PERSON><PERSON> phím cách để lấy và sắp xếp lại", "content-manager.dnd.reorder": "{item}, moved. New position in list: {position}.", "content-manager.edit-settings-view.link-to-ctb.components": "Chỉnh sửa thành phần", "content-manager.edit-settings-view.link-to-ctb.content-types": "Chỉnh sửa loại nội dung", "content-manager.emptyAttributes.button": "<PERSON><PERSON><PERSON><PERSON> đến Trình tạo loại bộ sưu tập", "content-manager.emptyAttributes.description": "Thêm trường đầu tiên và<PERSON> bộ sưu tập của bạn", "content-manager.emptyAttributes.title": "<PERSON><PERSON><PERSON> có trườ<PERSON> nào hết", "content-manager.error.attribute.key.taken": "<PERSON><PERSON>á trị này đã tồn tại", "content-manager.error.attribute.sameKeyAndName": "<PERSON><PERSON><PERSON>ng thể bằng nhau", "content-manager.error.attribute.taken": "Tên trường này đã tồn tại", "content-manager.error.contentTypeName.taken": "Tên này đã tồn tại", "content-manager.error.model.fetch": "Một lỗi đã xảy ra trong khi lấy về cấu hình nội dung.", "content-manager.error.record.create": "Một lỗi đã xảy ra trong khi tạo bản ghi.", "content-manager.error.record.delete": "Một lỗi đã xảy ra trong khi xoá bản ghi.", "content-manager.error.record.fetch": "Một lỗi đã xảy ra trong khi lấy về bản ghi.", "content-manager.error.record.update": "Một lỗi đã xảy ra trong khi cập nhật bản ghi.", "content-manager.error.records.count": "Một lỗi đã xảy ra trong khi lấy về số lượng bản ghi.", "content-manager.error.records.fetch": "Một lỗi đã xảy ra trong khi lấy về các bản ghi.", "content-manager.error.schema.generation": "Một lỗi đã xảy ra trong khi quá trình tạo ra l<PERSON><PERSON><PERSON> đồ.", "content-manager.error.validation.json": "<PERSON><PERSON><PERSON> không phải là định dạng JSON", "content-manager.error.validation.max": "<PERSON><PERSON><PERSON> trị quá cao.", "content-manager.error.validation.maxLength": "<PERSON><PERSON><PERSON> trị quá dài.", "content-manager.error.validation.min": "<PERSON><PERSON><PERSON> trị quá thấp.", "content-manager.error.validation.minLength": "<PERSON><PERSON><PERSON> trị quá ngắn.", "content-manager.error.validation.minSupMax": "Can't over exot", "content-manager.error.validation.regex": "<PERSON><PERSON>á trị không khới với regex.", "content-manager.error.validation.required": "<PERSON><PERSON><PERSON> tr<PERSON> này b<PERSON> bu<PERSON>.", "content-manager.form.Input.bulkActions": "<PERSON><PERSON><PERSON> ho<PERSON>t hoạt động gộp", "content-manager.form.Input.defaultSort": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h sắp xếp mặc định", "content-manager.form.Input.description": "<PERSON><PERSON>", "content-manager.form.Input.description.placeholder": "<PERSON><PERSON><PERSON> hiển thị trong hồ sơ", "content-manager.form.Input.editable": "Trường chỉnh sửa được", "content-manager.form.Input.filters": "<PERSON><PERSON><PERSON> c<PERSON>c bộ lọc", "content-manager.form.Input.hint.character.unit": "{maxValue, plural, one { character} other { characters}}", "content-manager.form.Input.hint.minMaxDivider": " / ", "content-manager.form.Input.hint.text": "{min, select, undefined {} other {min. {min}}}{divider}{max, select, undefined {} other {max. {max}}}{unit}{br}{description}", "content-manager.form.Input.label": "<PERSON><PERSON>ã<PERSON>", "content-manager.form.Input.label.inputDescription": "<PERSON><PERSON><PERSON> trị này ghi đè lên nhãn được trình bày ở phần đầu của bảng", "content-manager.form.Input.pageEntries": "<PERSON><PERSON><PERSON> ghi trong trang", "content-manager.form.Input.pageEntries.inputDescription": "Lưu ý: <PERSON><PERSON><PERSON> có thể ghi đè giá trị này trong trang cài đặt Loại Bộ sưu tập.", "content-manager.form.Input.placeholder": "Placeholder", "content-manager.form.Input.placeholder.placeholder": "Placeholder", "content-manager.form.Input.search": "<PERSON><PERSON><PERSON> ho<PERSON>t tìm ki<PERSON>m", "content-manager.form.Input.search.field": "<PERSON><PERSON><PERSON> ho<PERSON>t tìm kiếm cho trường này", "content-manager.form.Input.sort.field": "<PERSON><PERSON><PERSON> ho<PERSON>t sắp xếp trên trư<PERSON><PERSON> này", "content-manager.form.Input.sort.order": "<PERSON><PERSON><PERSON> tự sắp xếp mặc định", "content-manager.form.Input.wysiwyg": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> n<PERSON> WYSIWYG", "content-manager.global.displayedFields": "<PERSON><PERSON>c <PERSON> Trình <PERSON>", "content-manager.groups": "Nhóm", "content-manager.groups.numbered": "Nhóm ({number})", "content-manager.header.name": "<PERSON><PERSON>i dung", "content-manager.link-to-ctb": "Chỉnh sửa mô hình", "content-manager.models": "<PERSON><PERSON><PERSON> bộ s<PERSON>u tập", "content-manager.models.numbered": "<PERSON><PERSON><PERSON> bộ sưu tập ({number})", "content-manager.notification.error.displayedFields": "Bạn cần trình bày ít nhất một trường", "content-manager.notification.error.relationship.fetch": "Một lỗi đã xảy ra khi tìm về mối quan hệ.", "content-manager.notification.info.SettingPage.disableSort": "<PERSON><PERSON>n cần có một thuộ<PERSON> t<PERSON>h đ<PERSON><PERSON><PERSON> phép sắp xếp", "content-manager.notification.info.minimumFields": "Bạn phải hiển thị ít nhất một trường", "content-manager.notification.upload.error": "<PERSON><PERSON> xảy ra lỗi khi tải lên các tập tin của bạn", "content-manager.pageNotFound": "<PERSON><PERSON><PERSON><PERSON>", "content-manager.pages.ListView.header-subtitle": "{number, plural, =0 {# entries} one {# entry} other {# entries}} found", "content-manager.pages.NoContentType.button": "<PERSON><PERSON><PERSON> nội dung đầu tiên của bạn", "content-manager.pages.NoContentType.text": "Bạn chưa có bất kỳ nội dung nào, chúng tôi khuyên bạn nên tạo Loại nội dung đầu tiên của mình.", "content-manager.permissions.not-allowed.create": "<PERSON>ạn không đ<PERSON><PERSON><PERSON> phép tạo tài liệu", "content-manager.permissions.not-allowed.update": "<PERSON>ạn không đ<PERSON><PERSON><PERSON> phép xem tài liệu này", "content-manager.plugin.description.long": "<PERSON><PERSON><PERSON> chóng để xem, chỉnh sửa và xóa dữ liệu trong cơ sở dữ liệu của bạn.", "content-manager.plugin.description.short": "<PERSON><PERSON><PERSON> chóng để xem, chỉnh sửa và xóa dữ liệu trong cơ sở dữ liệu của bạn.", "content-manager.popUpWarning.bodyMessage.contentType.delete": "Bạn có chắc chắn muốn xóa bản ghi này không?", "content-manager.popUpWarning.bodyMessage.contentType.delete.all": "Bạn có chắc chắn muốn xóa các bản ghi này không?", "content-manager.popUpWarning.bodyMessage.contentType.publish.all": "Bạn có chắc chắn muốn xuất bản những mục này không?", "content-manager.popUpWarning.bodyMessage.contentType.unpublish.all": "Bạn có chắc chắn muốn hủy xuất bản những mục này không?", "content-manager.popUpWarning.warning.has-draft-relations.title": "<PERSON><PERSON><PERSON>", "content-manager.popUpWarning.warning.publish-question": "Bạn vẫn muốn xuất bản?", "content-manager.popUpWarning.warning.unpublish": "<PERSON><PERSON><PERSON> bạn không xuất bản nội dung này, nó sẽ tự động chuyển thành <PERSON>ản nháp.", "content-manager.popUpWarning.warning.unpublish-question": "Bạn có chắc chắn không muốn xuất bản nó?", "content-manager.popUpWarning.warning.updateAllSettings": "<PERSON><PERSON> sẽ thay đổi tất cả cài đặt của bạn", "content-manager.popUpwarning.warning.has-draft-relations.button-confirm": "<PERSON><PERSON>, xu<PERSON><PERSON> bản", "content-manager.popUpwarning.warning.has-draft-relations.message": "<b>{count, plural, one { relation is } other { relations are } }</b> not published yet and might lead to unexpected behavior.", "content-manager.popUpwarning.warning.bulk-has-draft-relations.message": "<b>{count} {count, plural, one { relation } other { relations } } out of {entities} { entities, plural, one { entry } other { entries } } {count, plural, one { is } other { are } }</b> not published yet and might lead to unexpected behavior. ", "content-manager.popover.display-relations.label": "<PERSON><PERSON><PERSON> thị quan hệ", "content-manager.relation.add": "<PERSON><PERSON><PERSON><PERSON> mối quan hệ", "content-manager.relation.disconnect": "Xoá", "content-manager.relation.isLoading": "<PERSON><PERSON><PERSON> quan hệ đang tải", "content-manager.relation.loadMore": "<PERSON><PERSON><PERSON>ê<PERSON>", "content-manager.relation.notAvailable": "<PERSON><PERSON><PERSON><PERSON> có mối quan hệ nào", "content-manager.relation.publicationState.draft": "<PERSON><PERSON><PERSON>", "content-manager.relation.publicationState.published": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> h<PERSON>nh", "content-manager.reviewWorkflows.stage.label": "<PERSON><PERSON><PERSON> đo<PERSON>n xem xét", "content-manager.select.currently.selected": "{count} hi<PERSON><PERSON><PERSON><PERSON>n", "content-manager.success.record.delete": "Đã xóa", "content-manager.success.record.publish": "<PERSON><PERSON> đ<PERSON><PERSON><PERSON> p<PERSON> hành", "content-manager.success.record.publishing": "<PERSON><PERSON> xu<PERSON> bản...", "content-manager.success.record.save": "<PERSON><PERSON> l<PERSON>", "content-manager.success.record.unpublish": "<PERSON><PERSON><PERSON><PERSON><PERSON> xu<PERSON> bản", "content-manager.utils.data-loaded": "The {number, plural, =1 {entry has} other {entries have}} successfully been loaded", "content-manager.listView.validation.errors.title": "<PERSON><PERSON><PERSON> hành động", "content-manager.listView.validation.errors.message": "<PERSON><PERSON> lòng đảm bảo tất cả các trường đều hợp lệ trước khi xuất bản (trư<PERSON><PERSON> b<PERSON><PERSON> buộ<PERSON>, giới hạn ký tự tối thiểu/tối đa, v.v.)", "dark": "Dark", "form.button.continue": "<PERSON><PERSON><PERSON><PERSON>", "form.button.done": "Complete", "global.actions": "<PERSON><PERSON><PERSON> động", "global.auditLogs": "<PERSON><PERSON><PERSON><PERSON> ký kiểm tra", "global.back": "Quay lại", "global.cancel": "Hủy bỏ", "global.change-password": "<PERSON><PERSON><PERSON> mật kh<PERSON>u", "global.close": "Đ<PERSON><PERSON>", "global.content-manager": "<PERSON><PERSON><PERSON><PERSON>", "global.continue": "<PERSON><PERSON><PERSON><PERSON>", "global.delete": "Xóa bỏ", "global.delete-target": "Xóa {target}", "global.description": "<PERSON><PERSON>", "global.details": "<PERSON> ti<PERSON>", "global.disabled": "Disabled", "global.documentation": "<PERSON><PERSON><PERSON> l<PERSON>", "global.enabled": "<PERSON><PERSON> bật", "global.finish": "<PERSON><PERSON><PERSON> th<PERSON>", "global.marketplace": "Marketplace", "global.name": "<PERSON><PERSON><PERSON>", "global.none": "<PERSON><PERSON><PERSON><PERSON> có", "global.password": "<PERSON><PERSON><PERSON>", "global.plugins": "Plugins", "global.plugins.content-manager": "<PERSON><PERSON><PERSON><PERSON>", "global.plugins.content-manager.description": "<PERSON><PERSON><PERSON> chóng để xem, chỉnh sửa và xóa dữ liệu trong cơ sở dữ liệu của bạn.", "global.plugins.content-type-builder": "<PERSON><PERSON><PERSON><PERSON> tạo ki<PERSON>u nội dung", "global.plugins.content-type-builder.description": "<PERSON><PERSON> hình hóa cấu trúc dữ liệu của API của bạn. ", "global.plugins.documentation": "<PERSON><PERSON><PERSON> l<PERSON>", "global.plugins.documentation.description": "Tạo Tài liệu OpenAPI và trực quan hóa API của bạn bằng giao diện người dùng SWAGGER.", "global.plugins.email": "Email", "global.plugins.email.description": "<PERSON><PERSON><PERSON> cấu hình <PERSON>ng dụng của bạn để gửi email.", "global.plugins.graphql": "GraphQL", "global.plugins.graphql.description": "Thêm điểm cuối GraphQL với các phương thức API mặc định.", "global.plugins.i18n": "Internationalization", "global.plugins.i18n.description": "Plugin nà<PERSON> cho <PERSON><PERSON><PERSON> t<PERSON>, đ<PERSON><PERSON> và cập nhật nội dung bằng các ngôn ngữ khác nhau, cả từ Bảng quản trị và từ API.", "global.plugins.sentry": "Sentry", "global.plugins.sentry.description": "<PERSON><PERSON><PERSON> sự kiện lỗi St<PERSON>i tới <PERSON>.", "global.plugins.upload": "Quản lý media", "global.plugins.upload.description": "<PERSON><PERSON><PERSON><PERSON> lý tập tin phương tiện.", "global.plugins.users-permissions": "<PERSON>ai trò", "global.plugins.users-permissions.description": "Bảo vệ API của bạn bằng quy trình xác thực đầy đủ dựa trên JWT. ", "global.profile": "<PERSON><PERSON> sơ", "global.prompt.unsaved": "Bạn có chắc chắn muốn rời khỏi trang này? ", "global.reset-password": "Đặt lại mật khẩu", "global.roles": "<PERSON>ai trò", "global.save": "<PERSON><PERSON><PERSON>", "global.search": "<PERSON><PERSON><PERSON>", "global.see-more": "<PERSON><PERSON>", "global.select": "<PERSON><PERSON><PERSON>", "global.select-all-entries": "<PERSON><PERSON><PERSON> tất cả các mục", "global.settings": "Cài đặt", "global.type": "<PERSON><PERSON><PERSON>", "global.users": "<PERSON><PERSON><PERSON><PERSON> dùng", "light": "Light", "notification.contentType.relations.conflict": "<PERSON><PERSON><PERSON> nội dung có quan hệ xung đột", "notification.default.title": "Thông tin:", "notification.ee.warning.at-seat-limit.title": "{licenseLimitStatus, select, OVER_LIMIT {Over} AT_LIMIT {At}} seat limit ({currentUserCount}/{permittedSeats})", "notification.ee.warning.over-.message": "Add seats to {licenseLimitStatus, select, OVER_LIMIT {invite} AT_LIMIT {re-enable}} Users. If you already did it but it's not reflected in Strapi yet, make sure to restart your app.", "notification.error": "C<PERSON> lỗi đã xảy ra", "notification.error.invalid.configuration": "Bạn có cấu hình không hợp lệ, h<PERSON><PERSON> kiểm tra nhật ký máy chủ của bạn để biết thêm thông tin.", "notification.error.layout": "<PERSON><PERSON><PERSON><PERSON> thể khôi phục", "notification.error.tokennamenotunique": "Tên đã đ<PERSON><PERSON><PERSON> gán cho một mã thông báo khác", "notification.form.error.fields": "<PERSON><PERSON><PERSON> nhập liệu có vài lỗi", "notification.form.success.fields": "<PERSON><PERSON> lưu thay đổi", "notification.link-copied": "Đã sao chép liên kết vào bảng nhớ tạm", "notification.permission.not-allowed-read": "<PERSON>ạn không đ<PERSON><PERSON><PERSON> phép xem tài liệu này", "notification.success.apitokencreated": "API Token đã đư<PERSON><PERSON> tạo thành công", "notification.success.apitokenedited": "API Token đã được chỉnh sửa thành công", "notification.success.delete": "<PERSON><PERSON><PERSON> bị x<PERSON>a", "notification.success.saved": "<PERSON><PERSON> l<PERSON>", "notification.success.title": "<PERSON><PERSON><PERSON><PERSON> công:", "notification.success.transfertokencreated": "Transfer Token đã đư<PERSON><PERSON> tạo thành công", "notification.success.transfertokenedited": "Transfer Token đã được chỉnh sửa thành công", "notification.version.update.message": "Đã có phiên bản mới của <PERSON>rap<PERSON>!", "notification.warning.404": "404 - Not found", "notification.warning.title": "Cảnh báo:", "or": "HOẶC", "request.error.model.unknown": "<PERSON><PERSON><PERSON> trúc này không tồn tại", "selectButtonTitle": "<PERSON><PERSON><PERSON>", "skipToContent": "<PERSON><PERSON><PERSON><PERSON> đến nội dung", "submit": "Submit", "content-manager.popUpWarning.warning.cancelAllSettings": "Bạn có chắc chắn muốn hủy bỏ các thay đổi của mình không?", "Announcement": "<PERSON><PERSON><PERSON><PERSON> báo", "Article": "<PERSON><PERSON><PERSON> vi<PERSON>", "ArticleCat": "<PERSON><PERSON> m<PERSON><PERSON> b<PERSON>i viết", "IdentityVerification": "<PERSON><PERSON><PERSON> th<PERSON>c đ<PERSON>nh danh", "Order": "<PERSON><PERSON><PERSON> hàng", "Payment": "<PERSON><PERSON><PERSON> to<PERSON>", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "Product": "<PERSON><PERSON><PERSON> p<PERSON>m", "ProductCat": "<PERSON><PERSON> m<PERSON><PERSON> sản ph<PERSON>m", "User": "<PERSON><PERSON><PERSON> lý", "Transaction": "<PERSON><PERSON><PERSON><PERSON> chuy<PERSON> tiền", "manage-order.plugin.name": "<PERSON><PERSON><PERSON><PERSON> lý đơn hàng", "manage-user-tree.plugin.name": "<PERSON><PERSON><PERSON><PERSON> lý đại lý", "dashboard.plugin.name": "Dashboard", "manage-withdraw.plugin.name": "<PERSON><PERSON><PERSON><PERSON> lý yêu cầu rút tiền", "manage-promotion.plugin.name": "<PERSON><PERSON><PERSON><PERSON> lý k<PERSON>n mãi"}