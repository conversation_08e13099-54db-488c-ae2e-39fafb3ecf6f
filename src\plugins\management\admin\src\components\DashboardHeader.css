/* DashboardHeader CSS */
.dashboard-header {
  background: #ffffff;
  padding: 16px 24px;
  position: fixed;
  top: 0;
  right: 0;
  left: 256px;
  z-index: 999;
  transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.dashboard-header.collapsed {
  left: 70px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.left-section {
  display: flex;
  align-items: center;
}

.breadcrumb-text {
  color: #6b7280;
  font-size: 14px;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.right-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-container {
  position: relative;
}

.search-icon {
  width: 16px;
  height: 16px;
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.search-input {
  padding: 8px 16px 8px 40px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  font-family: 'Be Vietnam Pro', sans-serif;
  outline: none;
  transition: all 0.2s ease;
  width: 240px;
}

.search-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input::placeholder {
  color: #9ca3af;
}

.notification-button {
  position: relative;
  padding: 8px;
  background: transparent;
  border: none;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.notification-button:hover {
  background-color: #f3f4f6;
}

.bell-icon {
  width: 20px;
  height: 20px;
  color: #6b7280;
}

.notification-container {
  position: relative;
}

.notification-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 320px;
  max-height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  z-index: 1000;
  margin-top: 8px;
}

.notification-dropdown.hidden {
  display: none;
}

.notification-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.notification-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background-color: #f5f5f5;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.notification-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: #e6f7ff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.notification-text {
  flex: 1;
}

.notification-message {
  font-size: 13px;
  color: #262626;
  font-family: 'Be Vietnam Pro', sans-serif;
  margin-bottom: 4px;
}

.notification-time {
  font-size: 12px;
  color: #8c8c8c;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.notification-empty {
  padding: 40px 16px;
  text-align: center;
  color: #8c8c8c;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.user-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  background-color: #d1d5db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
}

.user-name {
  font-size: 14px;
  color: #374151;
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 500;
}

.loading-container {
  padding: 20px;
  text-align: center;
}
