{"kind": "collectionType", "collectionName": "khuyen_mais", "info": {"singularName": "khuyen-mai", "pluralName": "khuyen-mais", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "description": {"type": "text", "required": false}, "code": {"type": "string", "required": true, "unique": true}, "type": {"type": "enumeration", "required": true, "enum": ["percentage", "fixed_amount", "free_shipping", "buy_x_get_y"]}, "value": {"type": "decimal", "required": true, "default": 0}, "minOrderAmount": {"type": "decimal", "required": true, "default": 0}, "maxDiscountAmount": {"type": "decimal", "required": false}, "usageLimit": {"type": "integer", "required": false}, "usageCount": {"type": "integer", "required": true, "default": 0}, "startDate": {"type": "datetime", "required": true}, "endDate": {"type": "datetime", "required": true}, "isActive": {"type": "boolean", "required": true, "default": true}, "isPublic": {"type": "boolean", "required": true, "default": true}, "applicableProducts": {"type": "relation", "relation": "oneToMany", "target": "api::san-pham.san-pham"}, "applicableCategories": {"type": "relation", "relation": "oneToMany", "target": "api::danh-muc-san-pham.danh-muc-san-pham"}}}