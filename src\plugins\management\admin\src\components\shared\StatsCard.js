const React = require('react');
const {
  StatCard,
  StatContent,
  StatInfo,
  StatTitle,
  StatValue,
  StatIcon,
} = require('./StyledComponents');

const StatsCard = ({ title, value, icon, color }) => {
  return React.createElement(
    StatCard,
    null,
    React.createElement(
      StatContent,
      null,
      React.createElement(
        StatInfo,
        null,
        React.createElement(StatTitle, null, title),
        React.createElement(StatValue, null, value)
      ),
      React.createElement(StatIcon, { $color: color }, icon)
    )
  );
};

module.exports = StatsCard;
