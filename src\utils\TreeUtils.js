'use strict';

// function recursiveAddUser(output, tree) {
//     const {children, ...rest} = tree;
//     output.push(rest);
//      tree.children?.forEach((i) => {
//         const {children, ...rest} = i;
// }

function toArray(tree) {
  if (!tree) {
    return [];
  }
  const result = [];
  const { children, ...rest } = tree;
  result.push(rest);
  if (tree.children) {
    tree.children.forEach((child) => {
      result.push(...toArray(child));
    });
  }
  return result;
}

function buildBTreeWithoutBlocked(users) {
  const tree = createBinaryTree(users);
  let leftUsers = [];
  let rightUsers = [];
  if (tree.children.length === 2) {
    leftUsers = toArray(tree.children[0])
      .filter((o, index) => index === 0 || !o.blocked)
      .sort((o1, o2) => o1.createdAt - o2.createdAt);
    rightUsers = toArray(tree.children[1])
      .filter((o, index) => index === 0 || !o.blocked)
      .sort((o1, o2) => o1.createdAt - o2.createdAt);
  }
  return {
    ...tree,
    children: [createBinaryTree(leftUsers), createBinaryTree(rightUsers)],
  };
}

function createBinaryTree(arr) {
  function buildTree(nodes) {
    if (nodes.length === 0) {
      return null;
    }

    const rootData = nodes[0];
    const root = {
      ...rootData,
      children: [],
    };

    const leftNodes = [];
    const rightNodes = [];

    for (let i = 1; i < nodes.length; i++) {
      if (i % 2 === 1) {
        nodes[i].left = true;
        leftNodes.push(nodes[i]);
      } else {
        nodes[i].right = true;
        rightNodes.push(nodes[i]);
      }
    }

    const leftChild = buildTree(leftNodes);
    const rightChild = buildTree(rightNodes);

    if (leftChild !== null || rightChild !== null) {
      leftChild && root.children.push(leftChild);
      rightChild && root.children.push(rightChild);
    }

    return root;
  }

  // Bắt đầu đệ quy từ phần tử đầu tiên
  const binaryTree = buildTree(arr);

  return binaryTree;
}

const getNode = (saleObj, level) => ({
  name: saleObj.phone,
  attributes: {
    name: saleObj.name,
    sale: saleObj.mySale,
    teamSale: saleObj.myTeamSale,
    referCode: saleObj.referCode,
    fLevel: level,
    level: saleObj.level,
    id: saleObj.id,
  },
});

const getChildren = (users, parentId, level) => {
  const children = users.filter((o) => o.fParent === parentId);
  return children.map((o) => ({
    ...getNode(o, level),
    children: getChildren(users, o.id, level + 1),
  }));
};

async function getFTree() {
  const users = await strapi.db
    .query('plugin::users-permissions.user')
    .findMany({ where: { blocked: false } });
  const root = users.find((o) => o.fParent === null);
  const output = {
    ...getNode(root, 0),
    children: getChildren(users, root.id, 1),
  };
  return output;
}

module.exports = {
  getFTree,
  createBinaryTree,
  buildBTreeWithoutBlocked,
};
