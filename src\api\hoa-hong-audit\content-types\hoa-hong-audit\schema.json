{"kind": "collectionType", "collectionName": "hoa_hong_audits", "info": {"singularName": "hoa-hong-audit", "pluralName": "hoa-hong-audits", "displayName": "<PERSON><PERSON> hồng <PERSON>"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"hoaHong": {"type": "relation", "relation": "manyToOne", "target": "api::hoa-hong.hoa-hong"}, "action": {"type": "enumeration", "required": true, "enum": ["create", "update", "delete"]}, "field": {"type": "string", "required": true}, "oldValue": {"type": "text"}, "newValue": {"type": "text"}, "changedBy": {"type": "relation", "relation": "oneToOne", "target": "admin::user"}, "reason": {"type": "text"}}}