const React = require('react');

// Import CSS
require('./SharedComponents.css');

// Helper function to create elements with className
const createStyledElement = (tag, className, additionalProps = {}) => {
  return (props) => {
    const { children, className: propsClassName, ...restProps } = props || {};
    const finalClassName = propsClassName 
      ? `${className} ${propsClassName}` 
      : className;
    
    return React.createElement(
      tag,
      {
        className: finalClassName,
        ...additionalProps,
        ...restProps,
      },
      children
    );
  };
};

// Helper function for buttons with variants
const createButton = (baseClassName) => {
  return (props) => {
    const { 
      children, 
      className: propsClassName, 
      $variant, 
      variant,
      ...restProps 
    } = props || {};
    
    const variantClass = $variant || variant;
    let className = baseClassName;
    
    if (variantClass === 'primary') {
      className += ' btn-primary';
    } else if (variantClass === 'success') {
      className += ' btn-success';
    } else if (variantClass === 'view') {
      className += ' view';
    } else if (variantClass === 'edit') {
      className += ' edit';
    } else if (variantClass === 'delete') {
      className += ' delete';
    } else {
      className += ' btn-default';
    }
    
    if (propsClassName) {
      className += ` ${propsClassName}`;
    }
    
    return React.createElement(
      'button',
      {
        className,
        ...restProps,
      },
      children
    );
  };
};

// Helper function for stat icon with color
const createStatIcon = () => {
  return (props) => {
    const { 
      children, 
      className: propsClassName, 
      $color,
      color,
      ...restProps 
    } = props || {};
    
    const iconColor = $color || color || 'bg-blue';
    let className = 'stat-icon';
    
    // Handle hex colors or predefined classes
    if (iconColor.startsWith('#')) {
      // For hex colors, we'll use inline style
      const style = { backgroundColor: iconColor };
      return React.createElement(
        'div',
        {
          className: propsClassName ? `${className} ${propsClassName}` : className,
          style,
          ...restProps,
        },
        children
      );
    } else {
      // For predefined classes
      className += ` ${iconColor}`;
      if (propsClassName) {
        className += ` ${propsClassName}`;
      }
      
      return React.createElement(
        'div',
        {
          className,
          ...restProps,
        },
        children
      );
    }
  };
};

// Helper function for image container with size
const createImageContainer = () => {
  return (props) => {
    const { 
      children, 
      className: propsClassName, 
      size = 40,
      ...restProps 
    } = props || {};
    
    const style = {
      width: `${size}px`,
      height: `${size}px`,
    };
    
    const className = propsClassName 
      ? `image-container ${propsClassName}` 
      : 'image-container';
    
    return React.createElement(
      'div',
      {
        className,
        style,
        ...restProps,
      },
      children
    );
  };
};

// Helper function for placeholder container with size
const createPlaceholderContainer = () => {
  return (props) => {
    const { 
      children, 
      className: propsClassName, 
      size = 40,
      ...restProps 
    } = props || {};
    
    const style = {
      width: `${size}px`,
      height: `${size}px`,
    };
    
    const className = propsClassName 
      ? `placeholder-container ${propsClassName}` 
      : 'placeholder-container';
    
    return React.createElement(
      'div',
      {
        className,
        style,
        ...restProps,
      },
      children
    );
  };
};

// Container Components
const PageContainer = createStyledElement('div', 'page-container');

// Card Components
const Card = createStyledElement('div', 'card');
const CardHeader = createStyledElement('div', 'card-header');
const HeaderInfo = createStyledElement('div', 'header-info');
const CardTitle = createStyledElement('h2', 'card-title');
const CardDescription = createStyledElement('p', 'card-description');
const HeaderActions = createStyledElement('div', 'header-actions');
const CardContent = createStyledElement('div', 'card-content');

// Button Components
const Button = createButton('btn');

// Search Components
const FiltersSection = createStyledElement('div', 'filters-section');
const SearchContainer = createStyledElement('div', 'search-container');
const SearchInput = createStyledElement('input', 'search-input');

// Filter Components
const FilterGroup = createStyledElement('div', 'filter-group');
const FilterLabel = createStyledElement('span', 'filter-label');
const DateInput = createStyledElement('input', 'date-input');
const SelectInput = createStyledElement('select', 'select-input');

// Stats Components
const StatsGrid = createStyledElement('div', 'stats-grid');
const StatCard = createStyledElement('div', 'stat-card');
const StatContent = createStyledElement('div', 'stat-content');
const StatInfo = createStyledElement('div', 'stat-info');
const StatTitle = createStyledElement('p', 'stat-title');
const StatValue = createStyledElement('p', 'stat-value');
const StatIcon = createStatIcon();

// Table Components
const TableContainer = createStyledElement('div', 'table-container');

// Loading Components
const LoadingContainer = createStyledElement('div', 'loading-container');

// Empty State Components
const EmptyContainer = createStyledElement('div', 'empty-container');
const EmptyIcon = createStyledElement('div', 'empty-icon');
const EmptyTitle = createStyledElement('h3', 'empty-title');
const EmptyDescription = createStyledElement('p', 'empty-description');

// Action Button Components
const ActionButtons = createStyledElement('div', 'action-buttons');
const ActionButton = createButton('action-button');

// Image Components
const ImageContainer = createImageContainer();
const PlaceholderContainer = createPlaceholderContainer();

// Table Components
const StyledTable = createStyledElement('div', 'styled-table');

// Animation keyframe (for reference, actual animation is in CSS)
const spin = 'spin';

// Export all styled components
module.exports = {
  spin,
  PageContainer,
  Card,
  CardHeader,
  HeaderInfo,
  CardTitle,
  CardDescription,
  HeaderActions,
  CardContent,
  Button,
  FiltersSection,
  SearchContainer,
  SearchInput,
  FilterGroup,
  FilterLabel,
  DateInput,
  SelectInput,
  StatsGrid,
  StatCard,
  StatContent,
  StatInfo,
  StatTitle,
  StatValue,
  StatIcon,
  TableContainer,
  LoadingContainer,
  EmptyContainer,
  EmptyIcon,
  EmptyTitle,
  EmptyDescription,
  ActionButtons,
  ActionButton,
  ImageContainer,
  PlaceholderContainer,
  StyledTable,
};
