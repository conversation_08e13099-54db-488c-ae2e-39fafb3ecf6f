{"kind": "collectionType", "collectionName": "hoa_hongs", "info": {"singularName": "hoa-hong", "pluralName": "hoa-hongs", "displayName": "<PERSON><PERSON> hồ<PERSON>"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"order": {"type": "relation", "relation": "manyToOne", "target": "api::don-hang.don-hang", "inversedBy": "hoa_hongs"}, "user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "amount": {"type": "integer", "required": true}, "percentage": {"type": "decimal", "required": true, "default": 10}, "statusPaid": {"type": "enumeration", "required": true, "default": "pending", "enum": ["pending", "paid", "cancelled"]}, "type": {"type": "enumeration", "required": true, "enum": ["referral", "company"]}}}