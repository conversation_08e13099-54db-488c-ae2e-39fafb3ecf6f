import selectCollectionTypesRelatedPermissions from '@strapi/plugin-i18n/admin/src/selectors/selectCollectionTypesRelatedPermissions';
import { useSelector } from 'react-redux';

export default function checkPermission(slug) {
  const collectionTypesRelatedPermissions = useSelector(
    selectCollectionTypesRelatedPermissions
  );
  const read =
    !!collectionTypesRelatedPermissions[slug]?.[
      'plugin::content-manager.explorer.read'
    ];
  const update =
    !!collectionTypesRelatedPermissions[slug]?.[
      'plugin::content-manager.explorer.update'
    ];
  return { hasPermission: read, read, update };
}
