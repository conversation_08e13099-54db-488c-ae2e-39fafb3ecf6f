'use strict';

const { yup, validateYupSchema } = require('@strapi/utils');
const {
  TransactionType,
  LevelByF1,
  OrderStatus,
  DiscountSecondOrder,
  BossAccounts,
  LevelName,
  Level,
  BonusPercent,
  BonusPercentForChild,
  MinRevenueForLevel,
  CommissionPercentForParent,
} = require('./constants');
const { formatPrice } = require('./CommonUtils');
const dayjs = require('dayjs');
const CryptoJS = require('crypto-js');

const registerSchema = yup.object({
  phone: yup.string().required(),
  password: yup.string().required(),
});

function convertPhone11To10(phone) {
  const filter = {
    84: '0',
    '+84': '0',
    '016': '03',
    '0120': '070',
    '0121': '079',
    '0122': '077',
    '0126': '076',
    '0128': '078',
    '0123': '083',
    '0124': '084',
    '0125': '085',
    '0127': '081',
    '0129': '082',
    '0188': '058',
    '0186': '056',
    '0199': '059',
  };
  const keys = Object.keys(filter);
  for (const key of keys) {
    if (phone.startsWith(key)) {
      const phone2 = phone.replace(key, filter[key]);
      for (const key2 of keys) {
        if (phone2.startsWith(key2)) {
          return phone2.replace(key2, filter[key2]);
        }
      }
      return phone2;
    }
  }
  return phone;
}

function genRand() {
  let result = '';
  const characters = '0123456789';
  const charactersLength = characters.length;
  for (let i = 0; i < 6; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

const calcUserLevel = async (user) => {
  const u = await strapi.db.query('plugin::users-permissions.user').findOne({
    where: { id: user.id },
  });
  return calcUserLevelUpdated(u);
};

const calcUserLevelUpdated = async (u) => {
  let level = 0;
  const month = dayjs().month() + 1;
  const year = dayjs().year();
  const saleInMonthByUser = await getSaleInMonthByUser(u.id, month, year);
  let totalSaleUpLevel =
    parseInt(saleInMonthByUser.sale) + parseInt(saleInMonthByUser.myTeamSale);

  const mapCountLevelChild = {
    [Level.BronzeMember]: 0,
    [Level.SilverMember]: 0,
    [Level.GoldenMember]: 0,
    [Level.PlatinumMember]: 0,
    [Level.DiamondMember]: 0,
  };
  const children = u.sChildren ? u.sChildren.split(',') : [];
  if (children?.length) {
    const users = await strapi.db
      .query('plugin::users-permissions.user')
      .findMany({
        where: {
          id: { $in: children },
        },
      });
    for (const user of users) {
      if (user.level > 0) mapCountLevelChild[user.level] += 1;
    }
  }
  //calculate sale table
  for (const key in MinRevenueForLevel) {
    if (totalSaleUpLevel >= MinRevenueForLevel[key]) {
      if (mapCountLevelChild[parseInt(key) - 1] >= 2) {
        level = key;
      }
    } else break;
  }
  return parseInt(level) > u.level ? parseInt(level) : null;
};

const updateLevelParent = async (newUser) => {
  const parent = await queryUserById(newUser.fParent, {
    populate: { fParent: true },
  });
  if (parent) {
    const level = await calcUserLevel(parent);

    if (level) {
      await strapi.db.query('plugin::users-permissions.user').update({
        where: { id: parent.id },
        data: { level },
      });
      await sendNotiBaseOnLevel(parent, level);
    }
    await updateLevelParent(parent);
  }
};
async function generateMessage({
  type,
  moneySource,
  amount,
  targetWallet,
  orderId,
}) {
  switch (type) {
    case TransactionType.Withdraw:
      return `Đã tạo yêu cầu rút ${formatPrice(amount)} về tài khoản của bạn`;
    case TransactionType.WithdrawToPTCD:
      return `Bạn bị trích ${formatPrice(amount)} từ ví tri ân về quỹ PTCD`;
    case TransactionType.CommissionOrder:
      return `Bạn được cộng ${formatPrice(amount)} từ đại lý mã ${moneySource}`;
    case TransactionType.CommissionSystem:
      let message = `Bạn được cộng ${formatPrice(amount)} hoa hồng `;
      if (targetWallet === 'main') {
        message += 'vào ví chính';
      } else {
        message += 'vào ví tri ân';
      }
      return message;
    case TransactionType.Manual:
      let message2 = `Bạn được cộng ${formatPrice(amount)} từ Admin `;
      if (targetWallet === 'main') {
        message2 += 'vào ví chính';
      } else {
        message2 += 'vào ví tri ân';
      }
      return message2;
    case TransactionType.CashbackTriAn:
      return `Bạn được hoa hồng ${formatPrice(
        amount
      )} vào ví tri ân từ đơn ${moneySource}-${orderId}`;
    case TransactionType.BonusTet:
      return `Bạn nhận được lì xì Tết ${formatPrice(amount)}`;
    case TransactionType.BonusForParentSecondOrder:
      return `Bạn nhận cộng ${formatPrice(
        amount
      )} hoa hồng từ đơn hàng thứ 2 của ${moneySource}`;
  }
}

async function addMoneyTo({
  to,
  amount,
  afterBalance,
  type,
  from = 0,
  moneySource = 0,
  targetWallet = 'main',
  orderId,
}) {
  trackEvent('addMoneyTo', {
    to,
    amount,
    afterBalance,
    type,
    from,
    moneySource,
    targetWallet,
    orderId,
  });
  let res;
  if (to === 0) {
    //withdraw
    if (targetWallet === 'main') {
      res = await strapi.db.query('plugin::users-permissions.user').update({
        where: { id: from },
        data: { balance: Number(afterBalance) },
      });
      trackEvent('addMoneySuccess', { type, to, targetWallet, res });
      await strapi.query('api::transaction.transaction').create({
        data: {
          from,
          to,
          amount,
          afterBalance,
          type,
          targetWallet,
          moneySource,
          orderId,
        },
      });
    } else if (targetWallet === 'sub') {
      res = await strapi.db.query('plugin::users-permissions.user').update({
        where: { id: from },
        data: { subBalance: afterBalance },
      });
      trackEvent('addMoneySuccess', { type, to, targetWallet, res });
      await strapi.query('api::transaction.transaction').create({
        data: {
          from,
          to,
          amount,
          afterBalance,
          type,
          moneySource,
          targetWallet,
          orderId,
        },
      });
    }
  } else if (to === -1) {
    //withdraw
    res = await strapi.db.query('plugin::users-permissions.user').update({
      where: { id: from },
      data: { subBalance: afterBalance },
    });
    trackEvent('addMoneySuccess', { type, to, res });
    await strapi.query('api::transaction.transaction').create({
      data: {
        from,
        to,
        amount,
        afterBalance,
        type,
        moneySource,
        orderId,
      },
    });
  } else {
    // deposit
    // let newBalance = afterBalance;
    // if (afterBalance === undefined) {
    //   const balance = await strapi.db
    //     .query('plugin::users-permissions.user')
    //     .findOne({
    //       where: { id: to },
    //     });
    //   newBalance = balance.balance + amount;
    // }
    const user = await queryUserById(to);
    const dataToUpdate =
      targetWallet === 'main'
        ? {
            balance: Number(afterBalance),
            commission: {
              ...user.commission,
              totalCommission: user.commission.totalCommission + amount,
              pendingCommission: Math.max(
                user.commission.pendingCommission - amount,
                0
              ),
            },
          }
        : { subBalance: afterBalance };
    res = await strapi.db.query('plugin::users-permissions.user').update({
      where: { id: to },
      data: dataToUpdate,
    });
    trackEvent('addMoneySuccess', { orderId, res });
    await strapi.query('api::transaction.transaction').create({
      data: {
        from,
        to,
        amount,
        afterBalance,
        type,
        moneySource,
        targetWallet,
        orderId,
      },
    });
  }
  const content = await generateMessage({
    type,
    moneySource,
    amount,
    targetWallet,
    orderId,
  });
  if (content) {
    await strapi.query('api::notification.notification').create({
      data: {
        user: from !== 0 && from !== -1 ? from : to,
        content,
      },
    });
    return res;
  }
}

async function sendNotiBaseOnLevel(user, level) {
  level &&
    (await strapi.query('api::notification.notification').create({
      data: {
        user: user.id,
        content: `Chúc mừng! Bạn đã đạt cấp độ ${LevelName[level]}!`,
      },
    }));
}

const trackEvent = async (name, { orderId, type, ...data }) => {
  return await strapi.db.query('api::tracking.tracking').create({
    data: { name, data, orderId, type },
  });
};

const trackError = async (name, data) => {
  return await strapi.db.query('api::tracking.tracking').create({
    data: { name, data, type: 'error' },
  });
};

const indexOfUserNP = async (u) => {
  if (u.status === 'inactive') return 0;
  return await strapi.db.query('plugin::users-permissions.user').count({
    where: {
      firstOrderAt: { $lte: u.firstOrderAt, $notNull: true },
      blocked: false,
      status: 'active',
    },
  });
};

const calculatePersonSale = async (userId) => {
  const orders = await strapi.entityService.findMany('api::order.order', {
    filters: {
      creator: userId,
      ...filterPaidOrder,
    },
  });

  const mySale = orders.reduce((total, order) => {
    return total + calcTotalPriceOrder(order);
  }, 0);
  await strapi.db.query('plugin::users-permissions.user').update({
    where: {
      id: userId,
    },
    data: {
      mySale,
    },
  });

  await calculateParentSale(userId);
  return mySale;
};

const calculateParentSale = async (userId) => {
  try {
    const mySale = await strapi.db
      .query('plugin::users-permissions.user')
      .findOne({
        where: { id: userId },
        populate: { fParent: true },
      });
    if (mySale?.fParent) {
      const myParent = await strapi.db
        .query('plugin::users-permissions.user')
        .findOne({
          where: {
            id: mySale.fParent,
          },
          populate: {
            sChildren: true,
            fParent: true,
          },
        });
      const total = await calcTotalGroup(myParent.id);

      await strapi.db.query('plugin::users-permissions.user').update({
        where: {
          id: mySale.fParent,
        },
        data: {
          myTeamSale: total,
        },
      });
      await calculateParentSale(mySale.fParent);
    }
  } catch (error) {
    trackError('error calculateParentSale', { error });
  }
};

/**
 * @description
 * parentId: number
 * loop: number | null | undefined
 * if you don't want to limit loop ==> null | undefined
 */
const calcTotalGroup = async (parentId, loop) => {
  let total = 0;
  if (!loop || loop > 0) {
    const arrChild = await strapi.db
      .query('plugin::users-permissions.user')
      .findMany({
        where: {
          fParent: parentId,
        },
        populate: {
          fParent: true,
        },
      });

    for (let child of arrChild) {
      const totalChildTeam = await calcTotalGroup(
        child.id,
        loop ? loop - 1 : null
      );

      total += (parseInt(child.mySale) || 0) + totalChildTeam;
    }
  }
  return total;
};

function calcDiscountSecondOrder(price) {
  for (const i in DiscountSecondOrder) {
    const currentDiscount =
      DiscountSecondOrder[DiscountSecondOrder.length - 1 - i];
    if (price >= currentDiscount.min && price < currentDiscount.max) {
      return price * currentDiscount.percent;
    }
  }
  return 0;
}

const inactiveUserAfterAMonth = async () => {
  const start = dayjs().subtract(1, 'month').startOf('month');
  const end = dayjs().subtract(1, 'month').endOf('month');
  const allUser = await strapi.db
    .query('plugin::users-permissions.user')
    .findMany({ where: { blocked: false, status: 'active' } });
  ///////////////////
  for (const u of allUser) {
    if (BossAccounts.includes(u.id)) continue;
    const lastMonthOrders = await strapi.db.query('api::order.order').findMany({
      where: {
        creator: u.id,
        $or: [
          { orderStatus: OrderStatus.Success },
          { orderStatus: OrderStatus.Shipping },
          { orderStatus: OrderStatus.Confirmed },
        ],
        createdAt: {
          $gte: start.toDate(),
          $lte: end.toDate(),
        },
      },
    });
    const totalPrice = lastMonthOrders.reduce((acc, order) => {
      return acc + Number(order.finalPrice);
    }, 0);
    if (totalPrice < ActiveIndex) {
      await strapi.db.query('plugin::users-permissions.user').update({
        where: { id: u.id },
        data: { status: 'inactive' },
      });
    }
  }
};

const filterPaidOrder = {
  $or: [
    { orderStatus: OrderStatus.Confirmed },
    { orderStatus: OrderStatus.Shipping },
    { orderStatus: OrderStatus.Success },
  ],
};

const decryptPassword = (password) => {
  try {
    const phoneDecryptedBytes = CryptoJS.AES.decrypt(
      password.toString(),
      CryptoJS.enc.Utf8.parse(process.env.JWT_SECRET),
      { iv: CryptoJS.enc.Utf8.parse('') }
    );
    return phoneDecryptedBytes.toString(CryptoJS.enc.Utf8);
  } catch (err) {
    return password;
  }
};

const queryUserById = (id, filter) => {
  return strapi.db
    .query('plugin::users-permissions.user')
    .findOne({ where: { id }, ...filter });
};

const isChildOf = async (child, parent) => {
  if (!child || !parent) return false;
  const childUser = await strapi.db
    .query('plugin::users-permissions.user')
    .findOne({
      where: { id: child },
    });
  if (!childUser) return false;
  if (childUser.fParent === parent) return true;
  if (childUser.fParent) {
    return await isChildOf(childUser.fParent, parent);
  }
  return false;
};

const getAllChildrenOf = async (output, userId) => {
  const children = await strapi.db
    .query('plugin::users-permissions.user')
    .findMany({
      where: {
        fParent: userId,
      },
      select: ['id'],
    });
  output.push(...children.map((i) => i.id));
  for (const child of children) {
    await getAllChildrenOf(output, child.id);
  }
};

const getChildrenOf = async (userId) => {
  return await strapi.db.query('plugin::users-permissions.user').findMany({
    where: {
      fParent: userId,
    },
    select: ['id'],
  });
};

const calculateCommissionForLevel = (order, level) => {
  const products = order.orderData.items || [];
  const bonusPercent = BonusPercent[level] || 0;
  return calculateCommission(products, bonusPercent);
};

const calculateCommissionForParentLevel = (order, parentLevel, childLevel) => {
  const products = order.orderData.items || [];
  const bonusPercent = BonusPercentForChild[parentLevel][childLevel] || 0;
  return calculateCommission(products, bonusPercent);
};

const calculateIndirectCommissionForParentLevel = (
  orderFinalPrice,
  parentLevel,
  childLevel
) => {
  const commissionPercent =
    CommissionPercentForParent[`${parentLevel}.${childLevel}`] || 0;

  return calculateIndirectCommission(orderFinalPrice, commissionPercent);
};

const calculateIndirectCommission = (orderFinalPrice, commissionPercent) => {
  const totalCommission = Math.round(orderFinalPrice * commissionPercent);
  return totalCommission;
};

const calculateCommission = (products, bonusPercent) => {
  let totalCommission = 0;
  for (const productData of products) {
    const { quantity, product } = productData;
    const discount = parseInt(product.discount) || 0;
    const productPrice = parseInt(product.price) || 0;
    const price = productPrice - discount;

    const isFixedCommission = product.isFixedCommission;
    if (isFixedCommission) {
      const commission = product.commission || 0;
      totalCommission += commission * quantity;
    } else {
      totalCommission += Math.round(price * quantity * bonusPercent);
    }
  }
  return totalCommission;
};

const orderOriginalPrice = (order) => {
  let orderTotalPrice = 0;
  const products = order.orderData.items || [];
  for (const productData of products) {
    const { quantity, product } = productData;
    const discount = parseInt(product.discount) || 0;
    const productPrice = parseInt(product.price) || 0;
    const price = productPrice - discount;
    const productTotalPrice = price * quantity;
    orderTotalPrice += productTotalPrice;
  }
  return orderTotalPrice;
};

const calculateMonthSale = async (userId) => {
  try {
    const month = dayjs().month() + 1;
    const year = dayjs().year();
    const orderThisMonth = await strapi.db.query('api::order.order').findMany({
      where: {
        creator: userId,
        createdAt: {
          $gte: dayjs().startOf('month').toDate(),
          $lte: dayjs().endOf('month').toDate(),
        },
        ...filterPaidOrder,
      },
    });
    const monthSale = orderThisMonth.reduce(
      (total, order) => total + calcTotalPriceOrder(order),
      0
    );
    const saleMonth = await strapi.db.query('api::sale.sale').findOne({
      where: {
        userId,
        month,
        year,
      },
    });
    if (!saleMonth) {
      const res = await strapi.db.query('api::sale.sale').create({
        data: {
          userId,
          month: month,
          year: year,
          sale: monthSale,
        },
      });

      trackEvent('calculateMonthSale', {
        userId,
        res,
        saleValue: monthSale,
      });
    } else {
      const res = await strapi.db.query('api::sale.sale').update({
        where: { id: saleMonth.id },
        data: { sale: monthSale },
      });
      trackEvent('calculateMonthSale', {
        userId,
        res,
        saleValue: monthSale - saleMonth.sale,
        saleMonthId: saleMonth.id,
      });
    }
    await calculateParentMonthSale(userId, month, year);
  } catch (error) {
    trackError('error calculateMonthSale', { error });
  }
};
const calculateParentMonthSale = async (userId, month, year) => {
  try {
    const childUser = await strapi.db
      .query('plugin::users-permissions.user')
      .findOne({
        where: { id: userId },
        populate: { fParent: true },
      });
    if (childUser?.fParent) {
      const parentUser = await strapi.db
        .query('plugin::users-permissions.user')
        .findOne({
          where: {
            id: childUser.fParent,
          },
          populate: {
            sChildren: true,
            fParent: true,
          },
        });
      const total = await calcTotalSaleGroupInMonth(parentUser.id, month, year);
      const saleParentInMonth = await getSaleInMonthByUser(
        parentUser.id,
        month,
        year
      );
      const res = await strapi.db.query('api::sale.sale').update({
        where: { id: saleParentInMonth.id },
        data: { myTeamSale: total },
      });
      trackEvent('calculateParentMonthSale', {
        userId,
        res,
        saleValue: total,
        saleParentInMonthId: saleParentInMonth.id,
      });

      await calculateParentMonthSale(parentUser.id, month, year);
    }
  } catch (error) {
    console.error('error calculateParentMonthSale', error);
    trackError('error calculateParentMonthSale', { message: error.message });
  }
};

const calcTotalPriceOrder = (order) => {
  let total = 0;
  const products = order.orderData.items || [];
  for (const productData of products) {
    const { quantity, product } = productData;
    const discount = parseInt(product.discount) || 0;
    const productPrice = parseInt(product.price) || 0;
    const price = productPrice - discount;
    // product have commission not add to total
    if (!product.isFixedCommission) {
      total += price * quantity;
    }
  }
  return total;
};

const calcTotalSaleGroupInMonth = async (userId, month, year) => {
  const arrChild = await strapi.db
    .query('plugin::users-permissions.user')
    .findMany({
      where: {
        fParent: userId,
      },
      populate: {
        fParent: true,
      },
    });
  const arrChildId = arrChild.map((item) => item.id);
  const saleInMonthByUsers = await strapi.db.query('api::sale.sale').findMany({
    where: {
      userId: arrChildId,
      month: month,
      year: year,
    },
    populate: ['userId'],
  });
  let totalMonthSale = 0;
  for (const saleUser of saleInMonthByUsers) {
    totalMonthSale +=
      parseInt(saleUser.sale) +
      (await calcTotalSaleGroupInMonth(saleUser.userId.id, month, year));
  }
  return totalMonthSale;
};

const getSaleInMonthByUser = async (userId, month, year) => {
  const saleInMonthByUser = await strapi.db.query('api::sale.sale').findOne({
    where: {
      userId,
      month: month,
      year: year,
    },
  });
  if (!saleInMonthByUser) {
    const res = await strapi.db.query('api::sale.sale').create({
      data: {
        userId,
        month: month,
        year: year,
      },
    });
    return res;
  }
  return saleInMonthByUser;
};

const snakeToCamel = (str) => {
  return str.replace(/_([a-z])/g, (g) => g[1].toUpperCase());
};

module.exports = {
  validateRegisterBody: validateYupSchema(registerSchema),
  genRand,
  convertPhone11To10,
  calcUserLevel,
  calcUserLevelUpdated,
  addMoneyTo,
  sendNotiBaseOnLevel,
  trackEvent,
  trackError,
  indexOfUserNP,
  calculatePersonSale,
  calculateParentSale,
  calcDiscountSecondOrder,
  inactiveUserAfterAMonth,
  decryptPassword,
  filterPaidOrder,
  queryUserById,
  isChildOf,
  getAllChildrenOf,
  getChildrenOf,
  calculateCommissionForLevel,
  calculateCommissionForParentLevel,
  calculateMonthSale,
  updateLevelParent,
  orderOriginalPrice,
  calculateIndirectCommissionForParentLevel,
  snakeToCamel,
};
