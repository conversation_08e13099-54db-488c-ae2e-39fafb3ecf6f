const styled = require('styled-components');
const { keyframes } = styled;

// Animations
const spin = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;

// Container Components
const PageContainer = styled.div`
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI',
    'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans',
    'Helvetica Neue', sans-serif;
`;

// Card Components
const Card = styled.div`
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
`;

const CardHeader = styled.div`
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
`;

const HeaderInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const CardTitle = styled.h2`
  font-size: 20px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 4px 0;
`;

const CardDescription = styled.p`
  font-size: 14px;
  color: #6b7280;
  margin: 0;
`;

const HeaderActions = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const CardContent = styled.div`
  padding: 24px;
`;

// Button Components
const Button = styled.button`
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  text-decoration: none;

  ${(props) => {
    if (props.$variant === 'primary') {
      return `
        background: #2563eb;
        color: #ffffff;
        border-color: #2563eb;

        &:hover {
          background: #1d4ed8;
          border-color: #1d4ed8;
        }
      `;
    } else if (props.$variant === 'success') {
      return `
        background: #059669;
        color: #ffffff;
        border-color: #059669;

        &:hover {
          background: #047857;
          border-color: #047857;
        }
      `;
    } else {
      return `
        background: #ffffff;
        color: #374151;
        border-color: #d1d5db;

        &:hover {
          background: #f9fafb;
          border-color: #9ca3af;
        }
      `;
    }
  }}

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;

    &:hover {
      background: inherit;
      border-color: inherit;
    }
  }

  svg {
    width: 16px;
    height: 16px;
    margin-right: 8px;

    &.animate-spin {
      animation: ${spin} 1s linear infinite;
    }
  }
`;

// Search Components
const FiltersSection = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;

const SearchContainer = styled.div`
  position: relative;
  flex: 1;
  max-width: 320px;

  @media (max-width: 768px) {
    max-width: none;
  }

  svg {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    color: #9ca3af;
  }
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 6px 12px 6px 36px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #374151;
  background-color: #ffffff;
  outline: none;
  transition: border-color 0.2s ease;
  font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI',
    'Roboto', sans-serif;

  &::placeholder {
    color: #9ca3af;
  }

  &:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }
`;

// Filter Components
const FilterGroup = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const FilterLabel = styled.span`
  font-size: 14px;
  color: #6b7280;
  white-space: nowrap;
`;

const DateInput = styled.input`
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #374151;
  background-color: #ffffff;
  outline: none;
  transition: border-color 0.2s ease;
  font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI',
    'Roboto', sans-serif;

  &:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }
`;

const SelectInput = styled.select`
  padding: 6px 2px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #374151;
  background-color: #ffffff;
  outline: none;
  transition: border-color 0.2s ease;
  font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI',
    'Roboto', sans-serif;

  &:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }
`;

// Stats Components
const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
`;

const StatCard = styled.div`
  position: relative;
  overflow: hidden;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
`;

const StatContent = styled.div`
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const StatInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const StatTitle = styled.p`
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  margin: 0 0 4px 0;
`;

const StatValue = styled.p`
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin: 0;
`;

const StatIcon = styled.div`
  padding: 12px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${(props) => {
    // If it's a hex color (starts with #), use it directly
    if (props.$color.startsWith('#')) {
      return props.$color;
    }

    // Otherwise, use predefined color mapping
    switch (props.$color) {
      case 'bg-blue':
        return '#3b82f6';
      case 'bg-blue-dark':
        return '#2563eb';
      case 'bg-green':
        return '#10b981';
      case 'bg-red':
        return '#ef4444';
      default:
        return '#3b82f6';
    }
  }};

  svg {
    width: 24px;
    height: 24px;
    color: white;
  }
`;

// Table Components
const TableContainer = styled.div`
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

// Loading Components
const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  background: #ffffff;
  border-radius: 8px;
`;

// Empty State Components
const EmptyContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: #ffffff;
  border-radius: 8px;
  text-align: center;
`;

const EmptyIcon = styled.div`
  margin-bottom: 16px;
  color: #9ca3af;

  svg {
    width: 48px;
    height: 48px;
  }
`;

const EmptyTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
`;

const EmptyDescription = styled.p`
  font-size: 14px;
  color: #6b7280;
  margin: 0;
`;

// Action Button Components
const ActionButtons = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const ActionButton = styled.button`
  width: 32px;
  height: 32px;
  padding: 0;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  svg {
    width: 16px;
    height: 16px;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;

    &:hover {
      background: #ffffff !important;
      border-color: #d1d5db !important;
    }
  }

  .animate-spin {
    animation: ${spin} 1s linear infinite;
  }

  ${(props) => {
    switch (props.$variant) {
      case 'view':
        return `
          color: #2563eb;
          &:hover:not(:disabled) {
            background: #eff6ff;
            border-color: #2563eb;
          }
        `;
      case 'edit':
        return `
          color: #10b981;
          &:hover:not(:disabled) {
            background: #ecfdf5;
            border-color: #10b981;
          }
        `;
      case 'delete':
        return `
          color: #ef4444;
          &:hover:not(:disabled) {
            background: #fef2f2;
            border-color: #ef4444;
          }
        `;
      default:
        return '';
    }
  }}
`;

// Image Components
const ImageContainer = styled.div`
  width: ${(props) => props.size || 40}px;
  height: ${(props) => props.size || 40}px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;

  .ant-image {
    width: 100%;
    height: 100%;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 6px;
    }
  }
`;

const PlaceholderContainer = styled.div`
  width: ${(props) => props.size || 40}px;
  height: ${(props) => props.size || 40}px;
  background-color: #f5f5f5;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 12px;
  text-align: center;
  flex-shrink: 0;
`;

// Table Components
const StyledTable = styled.div`
  .ant-table {
    .ant-table-thead > tr > th {
      padding: 12px;
      background: #f8fafc;
      border-bottom: 1px solid #e5e7eb;
      font-weight: 600;
      color: #374151;
      font-size: 14px;
    }

    .ant-table-tbody > tr > td {
      padding: 12px;
      border-bottom: 1px solid #e5e7eb;
      font-size: 14px;
      color: #374151;
    }

    .ant-table-tbody > tr:hover > td {
      background: #f9fafb;
    }

    .ant-table-pagination {
      margin: 16px 0 0 0;

      .ant-pagination-item {
        border-radius: 6px;
      }

      .ant-pagination-item-active {
        background: #2563eb;
        border-color: #2563eb;

        a {
          color: #ffffff;
        }
      }
    }
  }
`;

// Export all styled components
module.exports = {
  spin,
  PageContainer,
  Card,
  CardHeader,
  HeaderInfo,
  CardTitle,
  CardDescription,
  HeaderActions,
  CardContent,
  Button,
  FiltersSection,
  SearchContainer,
  SearchInput,
  FilterGroup,
  FilterLabel,
  DateInput,
  SelectInput,
  StatsGrid,
  StatCard,
  StatContent,
  StatInfo,
  StatTitle,
  StatValue,
  StatIcon,
  TableContainer,
  LoadingContainer,
  EmptyContainer,
  EmptyIcon,
  EmptyTitle,
  EmptyDescription,
  ActionButtons,
  ActionButton,
  ImageContainer,
  PlaceholderContainer,
  StyledTable,
};
