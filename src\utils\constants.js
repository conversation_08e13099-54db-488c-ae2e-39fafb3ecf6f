const PhoneRegex = /^0[35789]\d{8}$/;
const MailRegex = /^\S+@\S+\.\S+$/;
const PhoneSubRegex = /^0[35789]\d{8}(-\d+)?$/;
const ReferCodeRegex = /^UK\d{1,10}$/;

// const Level = {
//   NewUser: 0,
//   BronzeMember: 1,
//   SilverMember: 2,
//   GoldenMember: 3,
//   PlatinumMember: 4,
//   DiamondMember: 5,
// };

const NewUserRegisterCommissionValue = 600000;
const NewUserRegisterCommissionValueForPresident = 700000;
const CommissionValueForVicePresident = 100000;

const Level = {
  Member: 0,
  GoldMember: 1,
  Collaborator: 2,
  BusinessDirector: 3,
  ViceCEO: 4,
  CEO: 5,
};

const LevelName = {
  [Level.Member]: 'Thành viên mới',
  [Level.GoldMember]: '<PERSON><PERSON><PERSON> sứ kết nối',
  [Level.Collaborator]: '<PERSON><PERSON><PERSON> sứ <PERSON>ita',
  [Level.BusinessDirector]: '<PERSON><PERSON><PERSON><PERSON> đốc kinh doanh',
  [Level.ViceCEO]: '<PERSON><PERSON> gi<PERSON><PERSON> đốc',
  [Level.CEO]: 'CEO',
};

const LevelBv = {
  [Level.Member]: 0.1,
  [Level.GoldMember]: 0.2,
  [Level.Collaborator]: 0.3,
  [Level.BusinessDirector]: 0.44,
  [Level.ViceCEO]: 0.5,
  [Level.CEO]: 0.55,
};

const DirectCommission = {
  [Level.Member]: 300000,
  [Level.GoldMember]: 300000,
  [Level.Collaborator]: 350000,
  [Level.BusinessDirector]: 350000,
  [Level.ViceCEO]: 400000,
  [Level.CEO]: 450000,
};

const IndirectCommission = {
  [Level.Collaborator]: {
    [Level.GoldMember]: 50000,
  },
  [Level.BusinessDirector]: {
    [Level.GoldMember]: 50000,
    [Level.Collaborator]: 50000,
  },
  [Level.ViceCEO]: {
    [Level.GoldMember]: 100000,
    [Level.Collaborator]: 100000,
    [Level.BusinessDirector]: 50000,
  },
  [Level.CEO]: {
    [Level.GoldMember]: 150000,
    [Level.Collaborator]: 150000,
    [Level.BusinessDirector]: 100000,
    [Level.ViceCEO]: 50000,
  },
};

const MinRevenueForLevel = {
  [Level.SilverMember]: 15_000_000,
  [Level.GoldenMember]: 50_000_000,
  [Level.PlatinumMember]: 250_000_000,
  [Level.DiamondMember]: 500_000_000,
};

const BonusPercentForChild = {
  [Level.BronzeMember]: [0, 0, 0, 0, 0, 0],
  [Level.SilverMember]: [0, 0.04, 0, 0, 0, 0],
  [Level.GoldenMember]: [0, 0.06, 0.02, 0, 0, 0],
  [Level.PlatinumMember]: [0, 0.08, 0.04, 0.02, 0, 0],
  [Level.DiamondMember]: [0, 0.09, 0.05, 0.03, 0.01, 0],
};

const OrderStatus = {
  Pending: 1,
  Confirmed: 2,
  Shipping: 3,
  Cancelled: 4,
  Success: 5,
  OutOfStock: 6,
  Deposited: 7,
};

const OrderStatusText = {
  [OrderStatus.Pending]: 'Chờ thanh toán',
  [OrderStatus.Deposited]: 'Đã đặt cọc',
  [OrderStatus.Confirmed]: 'Đã thanh toán',
  [OrderStatus.Shipping]: 'Đang giao hàng',
  [OrderStatus.Cancelled]: 'Đã huỷ',
  [OrderStatus.Success]: 'Đã hoàn thành',
};

const OrderIndex = [
  OrderStatus.Pending,
  OrderStatus.Confirmed,
  OrderStatus.Shipping,
  OrderStatus.Success,
];
const CourseStatusText = {
  [OrderStatus.Pending]: 'Chờ thanh toán',
  [OrderStatus.Cancelled]: 'Đã huỷ',
  [OrderStatus.Confirmed]: 'Đã hoàn thành',
};

const CourseIndex = [OrderStatus.Pending, OrderStatus.Confirmed];

const OrderShipStatus = {
  Delivering: 'delivering',
  Delivered: 'delivered',
  DeliveryFail: 'delivery_fail',
  Cancel: 'cancel',
};

const TransactionType = {
  CommissionOrder: 'CommissionOrder',
  CommissionSystem: 'CommissionSystem', //hh tri an 3% 14 tang
  Withdraw: 'Withdraw',
  Manual: 'Manual',
  CashbackSecondOrder: 'CashbackSecondOrder',
  CashbackTriAn: 'CashbackTriAn',
  WithdrawToPTCD: 'WithdrawToPTCD',
  BonusTet: 'BonusTet',
  BonusForParentSecondOrder: 'BonusForParentSecondOrder',
  CommissionAffiliate: 'CommissionAffiliate',
  BonusMonthForDiamondMember: 'BonusMonthForDiamondMember',
  BonusQuarterMember: 'BonusQuarterMember',
  PendingCommissionRefer: 'PendingCommissionRefer',
  CommissionRefer: 'CommissionRefer',
};

const TnxTypes = {
  [TransactionType.CashbackSecondOrder]: 'Hoàn tiền đơn thứ 2 (25%)',
  [TransactionType.CashbackTriAn]: 'Tri ân tái tiêu dùng (3%)',
  [TransactionType.CommissionOrder]: 'Hoa hồng đơn đầu',
  [TransactionType.CommissionSystem]: 'Hoa hồng tri ân',
  [TransactionType.Withdraw]: 'Rút tiền',
  [TransactionType.WithdrawToPTCD]: 'Trích tiền vào quỹ PTCD',
  [TransactionType.BonusTet]: 'Lì xì Tết',
  [TransactionType.BonusForParentSecondOrder]:
    'Hoa hồng cho người giới thiệu (5%)',
};

const GeneralNoti = {
  NewUser: 'Vui lòng định danh thông tin cá nhân để có thể đặt hàng',
  NewUserVerified: 'Chúc mừng bạn đã xác thực tài khoản thành công',
  Membership: 'Chúc mừng bạn đã đạt chi tiêu tối thiểu để trở thành Membership',
  Silver: 'Bạn cần chi tiêu tối thiểu 500.000đ/tháng để giữ chỉ số năng động',
};

const MaxLevel = 16;
const BonusPercentPerMonth = 0.03; //deprecated
const BonusPercentTriAnByLevel = [
  0.09, 0.05, 0.04, 0.04, 0.03, 0.03, 0.03, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01,
  0.01,
];
const PercentToMainWallet = 0.85;
const PercentToPTCD = 0.15;
const MoneyToCreateSubAccount = 500000;
const ActiveIndex = 500000;

const BossAccounts = [1, 3, 4];

const NodeMailerConfig = {
  host: 'smtp.sendgrid.net',
  port: 465,
  auth: {
    user: 'apikey',
    pass: '*********************************************************************',
  },
};
const DefaultEmail = '<EMAIL>';
const MaxSubBalance = 2000000;
const PayForLowLevelUser = 15000;

const month11Orders = [
  2615, 2623, 2629, 2639, 2641, 2648, 2652, 2656, 2666, 2668, 2672, 2673, 2677,
  2678, 2680, 2685, 2688, 2689, 2691, 2692, 2696, 2700, 2702, 2703, 2704, 2706,
  2712, 2714, 2715, 2722, 2723, 2728, 2729, 2730, 2732, 2734, 2736, 2739, 2742,
  2744, 2745,
];
const month12Orders = [
  2607, 2608, 2613, 2616, 2617, 2618, 2619, 2620, 2621, 2624, 2625, 2626, 2631,
  2636, 2638, 2642, 2644, 2646, 2647, 2649, 2657, 2658, 2660, 2662, 2663, 2664,
  2665, 2667, 2670, 2671, 2674, 2679, 2683, 2686, 2687, 2695, 2698, 2699, 2707,
  2708, 2709, 2710, 2711, 2713, 2716, 2717, 2718, 2719, 2720, 2726, 2727, 2731,
  2735, 2737, 2738, 2743,
];
const CashbackTriAn = 0.03;
const BonusForParentSecondOrder = 0.05;
const DiscountSecondOrder = [
  { min: 0, max: 10000000, percent: 0.25 },
  { min: 10000000, max: 20000000, percent: 0.3 },
  { min: 20000000, max: 50000000, percent: 0.35 },
  { min: 50000000, max: Number.MAX_SAFE_INTEGER, percent: 0.45 },
];

const monthsInQuarter = {
  1: [1, 2, 3],
  2: [4, 5, 6],
  3: [7, 8, 9],
  4: [10, 11, 12],
};
const BonusQuarter = [
  { minValue: 500_000_000, bonusPercent: 0.02 },
  { minValue: 250_000_000, bonusPercent: 0.017 },
  { minValue: 200_000_000, bonusPercent: 0.015 },
  {
    minValue: 100_000_000,
    bonusPercent: 0.012,
  },
  { minValue: 30_000_000, bonusPercent: 0.01 },
];

const ColabStatus = {
  Not_Waiting: 'Not waiting for Approve',
  Waiting_For_Approval: 'Waiting for Approve',
  Approved: 'Approved',
};

const ColabStatusName = {
  [ColabStatus.Not_Waiting]: 'Chưa đăng ký',
  [ColabStatus.Waiting_For_Approval]: 'Chờ duyệt',
  [ColabStatus.Approved]: 'Đã duyệt',
};

// process.env.TEST
//   ? '<EMAIL>'
//   : '<EMAIL>';
const PREFIX_CODE = 'AT';
const REFER_CODE_ROOT = `${PREFIX_CODE}1`;
const Vat = 1.08;
const BonusPointPercent = 0.05;

const CommissionPercentForParent = {
  [`${Level.CEO}F0.${Level.CEO}F1`]: 0.1,
  [`${Level.CEO}F0.${Level.CEO}F2`]: 0.02,
  [`${Level.Collaborator}.${Level.Customer}`]: 0.2,
  [`${Level.Agency}.${Level.Collaborator}`]: 0.2,
};

const VoucherType = {
  DiscountPrice: 'discountPrice',
  DiscountPercent: 'discountPercent',
  FreeShip: 'freeship',
};

const PaymentMethod = {
  COD: 1,
  VNPAY: 2,
};

const SelectShippingStatus = {
  SelfShipping: 1,
  OrderShipping: 2,
};

const ShippingText = {
  [SelectShippingStatus.SelfShipping]: 'Tự vận chuyển',
  [SelectShippingStatus.OrderShipping]: 'Giao cho đơn vị vận chuyển',
};

const OrderType = {
  COURSE: 'Khóa học',
  PRODUCT: 'Sản phẩm',
};

const PaymentMethods = {
  Cod: 1,
  Bank: 2,
};

const MethodText = {
  [PaymentMethods.Cod]: 'Thanh toán khi nhận hàng',
  [PaymentMethods.Bank]: 'Chuyển khoản ngân hàng',
};

module.exports = {
  REFER_CODE_ROOT,
  PREFIX_CODE,
  Vat,
  CountLevelF: 2,
  OrderStatus,
  OrderStatusText,
  OrderIndex,
  OrderShipStatus,
  BonusPercent: [0, 0.11, 0.15, 0.17, 0.19, 0.2],
  BonusPercentForChild,
  PhoneRegex,
  PhoneSubRegex,
  Level1Sale: 1500000,
  LimitValueSaleInMonth: 5_000_000,
  MinRevenueForLevel,
  LevelByF1: {
    [Level.SilverMember]: 50_000_000,
    [Level.GoldenMember]: 250_000_000,
    [Level.PlatinumMember]: 500_000_000,
    [Level.DiamondMember]: 200_000_000,
  },
  BonusRateForDiamondMemberPerMonth: 0.02,
  Level,
  LevelName,
  TransactionType,
  GeneralNoti,
  MaxLevel,
  BonusPercentPerMonth,
  BonusPercentTriAnByLevel,
  PercentToMainWallet,
  PercentToPTCD,
  MoneyToCreateSubAccount,
  ActiveIndex,
  NodeMailerConfig,
  MailRegex,
  MustVerifyPhone: false,
  ReferCodeRegex,
  DefaultEmail,
  MaxSubBalance,
  BossAccounts,
  PayForLowLevelUser,
  month11Orders,
  month12Orders,
  DiscountSecondOrder,
  CashbackTriAn,
  TnxTypes,
  BonusForParentSecondOrder,
  monthsInQuarter,
  BonusQuarter,
  ColabStatus,
  ColabStatusName,
  BonusPointPercent,
  CommissionPercentForParent,
  VoucherType,
  PaymentMethod,
  NewUserRegisterCommissionValue,
  NewUserRegisterCommissionValueForPresident,
  CommissionValueForVicePresident,
  ShippingText,
  SelectShippingStatus,
  DirectCommission,
  IndirectCommission,
  LevelBv,
  OrderType,
  CourseIndex,
  CourseStatusText,
  MethodText,
};
