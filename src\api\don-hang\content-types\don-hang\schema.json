{"kind": "collectionType", "collectionName": "don_hangs", "info": {"singularName": "don-hang", "pluralName": "don-hangs", "displayName": "<PERSON><PERSON><PERSON> hàng"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"code": {"type": "string", "required": true}, "priceAfterTax": {"type": "decimal", "required": true, "default": 0}, "taxAmount": {"type": "decimal", "required": true, "default": 0}, "discountAmount": {"type": "decimal", "required": true, "default": 0}, "shippingAmount": {"type": "decimal", "required": true, "default": 0}, "productSnapshot": {"type": "json", "required": false}, "customerSnapshot": {"type": "json", "required": false}, "statusOrder": {"type": "enumeration", "required": true, "default": "<PERSON>ờ x<PERSON>c n<PERSON>n", "enum": ["<PERSON>ờ x<PERSON>c n<PERSON>n", "<PERSON><PERSON> giao hàng", "<PERSON><PERSON> giao hàng", "<PERSON><PERSON> hoàn thành", "<PERSON><PERSON> hủy"]}, "paymentStatus": {"type": "boolean", "required": true, "default": false}, "user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "hoa_hongs": {"type": "relation", "relation": "oneToMany", "target": "api::hoa-hong.hoa-hong", "mappedBy": "order"}, "promotion": {"type": "relation", "relation": "oneToOne", "target": "api::khuyen-mai.khuyen-mai"}, "promotionCode": {"type": "string", "required": false}, "promotionDiscountAmount": {"type": "decimal", "required": false, "default": 0}}}