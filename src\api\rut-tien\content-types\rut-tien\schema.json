{"kind": "collectionType", "collectionName": "rut_tiens", "info": {"singularName": "rut-tien", "pluralName": "rut-tiens", "displayName": "<PERSON><PERSON><PERSON>"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user"}, "amount": {"type": "decimal", "required": true}, "bankName": {"type": "string", "required": true}, "bankAccount": {"type": "string", "required": true}, "accountHolder": {"type": "string", "required": true}, "statusPaid": {"type": "enumeration", "required": true, "default": "pending", "enum": ["pending", "approved", "rejected"]}, "note": {"type": "text", "required": false}, "adminNote": {"type": "text", "required": false}, "processedAt": {"type": "datetime", "required": false}, "processedBy": {"type": "relation", "relation": "manyToOne", "target": "admin::user"}}}