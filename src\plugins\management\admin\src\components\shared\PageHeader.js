const React = require('react');
const {
  <PERSON><PERSON><PERSON><PERSON>,
  HeaderInfo,
  CardTitle,
  CardDescription,
  HeaderActions,
} = require('./StyledComponents');

const PageHeader = ({ title, description, actions }) => {
  return React.createElement(
    <PERSON>Header,
    null,
    React.createElement(
      HeaderInfo,
      null,
      React.createElement(CardTitle, null, title),
      description && React.createElement(CardDescription, null, description)
    ),
    actions && React.createElement(HeaderActions, null, actions)
  );
};

module.exports = PageHeader;
