{"kind": "singleType", "collectionName": "thiet_lap_giao_diens", "info": {"singularName": "thiet-lap-giao-dien", "pluralName": "thiet-lap-giao-diens", "displayName": "<PERSON><PERSON><PERSON><PERSON> lập giao di<PERSON>n Mini App"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"logo": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "title": {"type": "string", "required": true, "default": "Evotech xin chào!"}, "subtitle": {"type": "string", "required": true, "default": "<PERSON><PERSON><PERSON> mới tốt lành👋"}, "colorType": {"type": "enumeration", "enum": ["solid", "gradient"], "default": "solid", "required": true}, "primaryColor": {"type": "customField", "customField": "plugin::color-picker.color", "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", "default": "#2563eb"}, "gradientStart": {"type": "customField", "customField": "plugin::color-picker.color", "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", "default": "#667eea"}, "gradientEnd": {"type": "customField", "customField": "plugin::color-picker.color", "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", "default": "#764ba2"}}}