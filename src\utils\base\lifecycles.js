module.exports = {
  baseAfterCreate(event) {
    const { result, params, action, model } = event;
    const { createdBy, updatedBy, ...rest } = result;
    strapi.entityService.create('api::audit-log.audit-log', {
      data: {
        contentType: model.singularName,
        action,
        result: rest,
        author: createdBy?.id,
        params,
      },
    });
  },
  baseAfterUpdate(event) {
    const { result, params, action, model } = event;
    const { createdBy, updatedBy, ...rest } = result;
    strapi.entityService.create('api::audit-log.audit-log', {
      data: {
        contentType: model.singularName,
        action,
        result: rest,
        author: updatedBy?.id,
        params,
      },
    });
  },
  baseAfterDelete(event) {
    const { result, params, action, model } = event;
    const { createdBy, updatedBy, ...rest } = result;
    strapi.entityService.create('api::audit-log.audit-log', {
      data: {
        contentType: model.singularName,
        action,
        result: rest,
        author: updatedBy?.id,
        params,
      },
    });
  },
};
