const React = require('react');
const { useState } = React;
const { Modal, Form, Input, message, Space, Button, Switch } = require('antd');
const { useFetchClient } = require('@strapi/helper-plugin');
const SharedImageUpload = require('./SharedImageUpload');

const QuickAddModal = ({
  visible,
  onCancel,
  type,
  onSuccess,
  showActiveSwitch = false,
  autoActive = true,
  title,
}) => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState([]);
  const [submitting, setSubmitting] = useState(false);
  const { post } = useFetchClient();

  const handleSubmit = async (values) => {
    if (submitting) return;

    setSubmitting(true);
    try {
      // Step 1: Upload file to media library first (if exists)
      let mediaId = null;
      if (fileList.length > 0 && fileList[0].originFileObj) {
        const uploadFormData = new FormData();
        uploadFormData.append('files', fileList[0].originFileObj);

        try {
          const uploadResponse = await post('/upload', uploadFormData);
          if (uploadResponse.data && uploadResponse.data.length > 0) {
            mediaId = uploadResponse.data[0].id;
          }
        } catch (uploadError) {
          console.error('Error uploading file:', uploadError);
          message.error('Không thể tải lên hình ảnh');
          setSubmitting(false);
          return;
        }
      }

      // Step 2: Create category/brand with media ID
      const isActiveValue = showActiveSwitch
        ? values.isActive !== undefined
          ? values.isActive
          : true
        : autoActive;

      const requestData = {
        name: values.name,
        isActive: isActiveValue,
      };

      // Add media reference
      if (mediaId) {
        requestData[type === 'category' ? 'image' : 'logo'] = mediaId;
      }

      if (type === 'category') {
        await post('/management/products/categories', { data: requestData });
        message.success('Tạo danh mục thành công');
      } else {
        // Add brand-specific fields
        requestData.description = values.description || '';
        requestData.website = values.website || '';

        await post('/management/products/brands', { data: requestData });
        message.success('Tạo thương hiệu thành công');
      }

      handleCancel();
      onSuccess?.();
    } catch (error) {
      console.error('Error creating:', error);
      message.error(
        error.response?.data?.message ||
          `Có lỗi xảy ra khi tạo ${
            type === 'category' ? 'danh mục' : 'thương hiệu'
          }`
      );
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setFileList([]);
    onCancel();
  };

  const modalTitle =
    title ||
    (type === 'category' ? 'Thêm danh mục mới' : 'Thêm thương hiệu mới');

  return React.createElement(
    Modal,
    {
      title: modalTitle,
      open: visible,
      onCancel: handleCancel,
      footer: null,
      width: 600,
    },
    React.createElement(
      Form,
      {
        form: form,
        layout: 'vertical',
        onFinish: handleSubmit,
        style: { marginTop: 24 },
        initialValues: { isActive: true },
      },
      React.createElement(
        Form.Item,
        {
          label: type === 'category' ? 'Tên danh mục' : 'Tên thương hiệu',
          name: 'name',
          rules: [
            {
              required: true,
              message: `Vui lòng nhập tên ${
                type === 'category' ? 'danh mục' : 'thương hiệu'
              }`,
            },
            {
              min: 2,
              message: `Tên ${
                type === 'category' ? 'danh mục' : 'thương hiệu'
              } phải có ít nhất 2 ký tự`,
            },
          ],
        },
        React.createElement(Input, {
          placeholder: `Nhập tên ${
            type === 'category' ? 'danh mục' : 'thương hiệu'
          }`,
          size: 'large',
        })
      ),

      type === 'brand' &&
        React.createElement(
          React.Fragment,
          null,
          React.createElement(
            Form.Item,
            { label: 'Mô tả', name: 'description' },
            React.createElement(Input.TextArea, {
              placeholder: 'Nhập mô tả thương hiệu',
              rows: 3,
              size: 'large',
            })
          ),
          React.createElement(
            Form.Item,
            { label: 'Website', name: 'website' },
            React.createElement(Input, {
              placeholder: 'https://example.com',
              size: 'large',
              type: 'url',
            })
          )
        ),
      React.createElement(
        Form.Item,
        { label: type === 'category' ? 'Hình ảnh' : 'Logo' },
        React.createElement(SharedImageUpload, {
          value: fileList,
          onChange: setFileList,
          maxCount: 1,
          accept: 'image/*',
          uploadText: 'Tải lên',
        })
      ),

      showActiveSwitch &&
        React.createElement(
          Form.Item,
          { label: 'Trạng thái', name: 'isActive', valuePropName: 'checked' },
          React.createElement(Switch, {
            checkedChildren: 'Hoạt động',
            unCheckedChildren: 'Tạm dừng',
          })
        ),
      React.createElement(
        Form.Item,
        { style: { marginBottom: 0, textAlign: 'right' } },
        React.createElement(
          Space,
          null,
          React.createElement(
            Button,
            { onClick: handleCancel, disabled: submitting },
            'Hủy'
          ),
          React.createElement(
            Button,
            {
              type: 'primary',
              htmlType: 'submit',
              loading: submitting,
              disabled: submitting,
            },
            type === 'category' ? 'Tạo danh mục' : 'Tạo thương hiệu'
          )
        )
      )
    )
  );
};

module.exports = QuickAddModal;
