const { OrderStatus } = require('./constants');

const querySuccessOrders = async (whereObj) => {
  const orders = await strapi.db.query('api::order.order').findMany({
    where: {
      ...whereObj,
      $or: [
        { orderStatus: OrderStatus.Success },
        { orderStatus: OrderStatus.Shipping },
        { orderStatus: OrderStatus.Confirmed },
      ],
    },
  });
  return orders;
};

const querySuccessOrdersBy = async (query) => {
  const orders = await strapi.db.query('api::order.order').findMany({
    ...query,
    where: {
      ...query.where,
      $or: [
        { orderStatus: OrderStatus.Success },
        { orderStatus: OrderStatus.Shipping },
        { orderStatus: OrderStatus.Confirmed },
      ],
    },
  });
  return orders;
};

module.exports = {
  querySuccessOrders,
  querySuccessOrdersBy,
};
