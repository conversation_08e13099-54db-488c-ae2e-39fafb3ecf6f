const React = require('react');
const { Upload, Button, message, Space } = require('antd');
const {
  UploadOutlined,
  FileOutlined,
  DeleteOutlined,
} = require('@ant-design/icons');

// Import CSS
require('./SharedComponents.css');

const FileUpload = ({
  value = [],
  onChange,
  accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt',
  maxCount = 5,
  maxSize = 10, // MB
  disabled = false,
}) => {
  const handleUpload = async (file) => {
    // Check file size
    const isLtMaxSize = file.size / 1024 / 1024 < maxSize;
    if (!isLtMaxSize) {
      message.error(`File phải nhỏ hơn ${maxSize}MB!`);
      return false;
    }

    // Check file count
    if (value.length >= maxCount) {
      message.error(`Chỉ được upload tối đa ${maxCount} file!`);
      return false;
    }

    try {
      // Create file object
      const fileObj = {
        uid: Date.now().toString(),
        name: file.name,
        status: 'done',
        url: URL.createObjectURL(file),
        originFileObj: file,
      };

      const newFileList = [...value, fileObj];
      onChange?.(newFileList);
      message.success('Upload file thành công!');
    } catch (error) {
      console.error('Upload error:', error);
      message.error('Upload file thất bại!');
    }

    return false; // Prevent default upload
  };

  const handleRemove = (file) => {
    const newFileList = value.filter((item) => item.uid !== file.uid);
    onChange?.(newFileList);
    
    // Revoke object URL to prevent memory leaks
    if (file.url && file.url.startsWith('blob:')) {
      URL.revokeObjectURL(file.url);
    }
  };

  const uploadProps = {
    beforeUpload: handleUpload,
    fileList: [],
    showUploadList: false,
    accept,
    disabled,
  };

  return React.createElement(
    'div',
    { className: 'file-upload-container' },
    [
      React.createElement(
        Upload,
        { key: 'upload', ...uploadProps },
        React.createElement(
          Button,
          {
            icon: React.createElement(UploadOutlined),
            disabled: disabled || value.length >= maxCount,
          },
          `Chọn file (${value.length}/${maxCount})`
        )
      ),

      value.length > 0 &&
        React.createElement(
          'div',
          { key: 'file-list', className: 'file-list-container' },
          value.map((file) =>
            React.createElement(
              'div',
              { key: file.uid, className: 'file-item' },
              [
                React.createElement(
                  Space,
                  { key: 'file-info', align: 'center' },
                  [
                    React.createElement(FileOutlined, { key: 'icon' }),
                    React.createElement(
                      'span',
                      { key: 'name' },
                      file.name
                    ),
                  ]
                ),
                React.createElement(
                  Button,
                  {
                    key: 'delete',
                    type: 'text',
                    size: 'small',
                    icon: React.createElement(DeleteOutlined),
                    onClick: () => handleRemove(file),
                    disabled,
                    danger: true,
                  }
                ),
              ]
            )
          )
        ),
    ]
  );
};

module.exports = FileUpload;
