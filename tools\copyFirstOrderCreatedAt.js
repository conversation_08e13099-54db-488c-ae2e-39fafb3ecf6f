const { Client } = require('pg');
const Sqlite = require('better-sqlite3');
const dayjs = require('dayjs');

const ttkoreaLocal = {
  host: 'localhost',
  port: 5432,
  database: 'ttkorea',
  user: 'postgres',
  password: 'postgres',
};

const ttkoreaTest = {
  host: '**************',
  port: 5432,
  database: 'ttkorea-test',
  user: 'postgres',
  password: 'Ttkorea123',
};

const ttkoreaProd = {
  host: '**************',
  port: 5432,
  database: 'ttkorea',
  user: 'postgres',
  password: 'Ttkorea123',
};

async function main() {
  const sqliteDB = new Sqlite('./.tmp/data.db', {});
  const postgresDB = new Client(ttkoreaLocal);
  await postgresDB.connect();

  const allUsers = sqliteDB
    .prepare(
      `SELECT first_order_created_at, first_order_at, id, refer_code
      FROM up_users
      WHERE first_order_at IS NOT NULL
      ORDER BY first_order_at ASC`
    )
    .all();
  console.log('🚀 ~ main ~ a:', allUsers.length);

  const allUsers2 = (
    await postgresDB.query(
      `SELECT first_order_created_at, first_order_at, id, refer_code
      FROM up_users
      WHERE first_order_at IS NOT NULL
      ORDER BY first_order_at ASC;`
    )
  ).rows;
  console.log('🚀 ~ main ~ allUsers2:', allUsers2.length);

  const dup = [];
  let temp = [];
  for (const i in allUsers2) {
    const u = allUsers2[allUsers2.length - 1 - i];
    const ubefore = allUsers2[allUsers2.length - 2 - i];
    // const u2 = allUsers2[allUsers2.length - 1 - i];
    // if (ubefore && ubefore.id === 105)
    //   console.log(
    //     'xxx',
    //     dayjs(u.first_order_at).unix(),
    //     dayjs(ubefore.first_order_at).unix(),
    //   );
    if (
      ubefore &&
      dayjs(u.first_order_at).unix() === dayjs(ubefore.first_order_at).unix()
    ) {
      temp.push(u.id);
    } else if (temp.length) {
      temp.push(u.id);
      dup.push(temp);
      temp = [];
    }
  }
  // console.log('xxxxx', dup);
  for (const dups of dup) {
    const listUser = sqliteDB
      .prepare(
        `SELECT first_order_created_at, first_order_at, id, refer_code
        FROM up_users
        WHERE first_order_at IS NOT NULL and id in (${dups.join(',')})
        ORDER BY first_order_at ASC`
      )
      .all();
    let first = dayjs(listUser[0].first_order_at);
    for (const u of listUser) {
      const q = `UPDATE up_users SET first_order_at=TO_TIMESTAMP(${first.unix()}::bigint) WHERE id=${
        u.id
      }`;
      // console.log('🚀 ~ q ~ q:', q);
      await postgresDB.query(q);
      first = first.add(1, 'second');
    }
    // break;
  }

  await postgresDB.end();
}

main();
