module.exports = [
  {
    en_name: 'Joint Stock Commercial Bank for Foreign Trade of Vietnam',
    vn_name: '<PERSON>ân hàng TMCP Ngoại Thương',
    bankId: '970436',
    bicCode: 'BFTVVNVN',
    atmBin: '970436',
    cardLength: 0,
    shortName: 'Vietcombank, VCB',
    bankCode: '203',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Vietnam Technological and Commercial Joint stock Bank',
    vn_name: '<PERSON>ân hàng Kỹ thương Việt Nam',
    bankId: '970407',
    bicCode: 'VTCBVNVN',
    atmBin: '970407',
    cardLength: 16,
    shortName: 'Techcombank, TCB',
    bankCode: '310',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Vietnam Technological and Commercial Joint stock Bank',
    vn_name: '<PERSON><PERSON> hàng Thương mại <PERSON> phần <PERSON>i<PERSON><PERSON>',
    bankId: '9704084',
    bicCode: 'TPB',
    atmBin: '9704084',
    cardLength: 16,
    shortName: 'Tienphongbank, TPB',
    bankCode: '3101',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Bank for Investment and Development of Vietnam',
    vn_name: 'Ngân hàng Đầu tư và Phát triển Việt Nam',
    bankId: '970418',
    bicCode: 'BIDVVNVN',
    atmBin: '970418',
    cardLength: 16,
    shortName: 'BIDV',
    bankCode: '202',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'An Binh Commercial Joint stock Bank',
    vn_name: 'Ngân hàng An Bình',
    bankId: '970425',
    bicCode: 'ABBKVNVN',
    atmBin: '970425',
    cardLength: 16,
    shortName: 'ABBank',
    bankCode: '323',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Asia Commercial Bank',
    vn_name: 'Ngân hàng Á Châu',
    bankId: '970416',
    bicCode: 'ASCBVNVN',
    atmBin: '970416',
    cardLength: 0,
    shortName: 'ACB',
    bankCode: '307',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Vienam Bank for Agriculture and Rural Development',
    vn_name: 'Ngân hàng NN & PTNT VN',
    bankId: '970405',
    bicCode: 'VBAAVNVN',
    atmBin: '970499',
    cardLength: 16,
    shortName: 'AGRIBANK',
    bankCode: '204',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'ANZ Bank',
    vn_name: 'Ngân hàng ANZ Việt Nam',
    cardLength: 0,
    shortName: 'ANZ',
    bankCode: '602',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Bac A Commercial Joint Stock Bank',
    vn_name: 'Ngân hàng Bắc Á',
    bankId: '970409',
    bicCode: 'NASCVNVN',
    atmBin: '970409',
    cardLength: 0,
    shortName: 'Bac A Bank',
    bankCode: '313',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'BANGKOK  BANK',
    vn_name: 'BANGKOK  BANK',
    cardLength: 0,
    shortName: 'BANGKOK  BANK',
    bankCode: '612',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'VietNam national Financial switching Joint Stock Company',
    vn_name: 'Công ty cổ phần chuyển mạch tài chính quốc gia Việt Nam',
    cardLength: 0,
    shortName: 'Banknetvn',
    bankCode: '401',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Bao Viet Joint Stock Commercial Bank',
    vn_name: 'Ngân hàng TMCP Bảo Việt',
    bankId: '970438',
    bicCode: 'BVBVVNVN',
    atmBin: '970438',
    cardLength: 20,
    shortName: 'BAOVIET Bank',
    bankCode: '359',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Bank for investment and development of Cambodia HCMC',
    vn_name: 'NH ĐT&PT Campuchia CN HCM',
    cardLength: 0,
    shortName: 'BIDC HCM',
    bankCode: '648',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Bank for investment and development of Cambodia HN',
    vn_name: 'NH ĐT&PT Campuchia CN Hà Nội',
    cardLength: 0,
    shortName: 'BIDC HN',
    bankCode: '638',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Bank of Paris and the Netherlands HCMC',
    vn_name: 'Ngân hàng BNP Paribas CN HCM',
    cardLength: 0,
    shortName: 'BNP Paribas HCM',
    bankCode: '614',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'BNP Paribas Hanoi',
    vn_name: 'Ngân hàng BNP Paribas CN Hà Nội',
    cardLength: 0,
    shortName: 'BNP Paribas HN',
    bankCode: '657',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'BANK OF CHINA',
    vn_name: 'BANK OF CHINA',
    cardLength: 0,
    shortName: 'BOC',
    bankCode: '620',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Bank of Communications',
    vn_name: 'Bank of Communications',
    cardLength: 0,
    shortName: 'BoCom',
    bankCode: '615',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Bank of India',
    vn_name: 'Bank of India',
    cardLength: 0,
    shortName: 'BOI',
    bankCode: '659',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'BPCEIOM HCMC',
    vn_name: 'Ngân hàng BPCEIOM CN  TP Hồ Chí Minh',
    cardLength: 0,
    shortName: 'BPCEICOM',
    bankCode: '601',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'BANK OF TOKYO - MITSUBISHI UFJ - TP HCM',
    vn_name: 'BANK OF TOKYO - MITSUBISHI UFJ - TP HCM',
    cardLength: 0,
    shortName: 'BTMU HCM',
    bankCode: '622',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'BANK OF TOKYO - MITSUBISHI UFJ - HN',
    vn_name: 'BANK OF TOKYO - MITSUBISHI UFJ - HN',
    cardLength: 0,
    shortName: 'BTMU HN',
    bankCode: '653',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Credit Agricole Corporate and Investment Bank',
    vn_name: 'Credit Agricole Corporate and Investment Bank',
    cardLength: 0,
    shortName: 'CACIB',
    bankCode: '621',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'CAKE by VPBank',
    vn_name: 'Ngân hàng số CAKE by VPBank',
    bankId: '546034',
    bicCode: 'YOLOVNVN',
    atmBin: '546034',
    cardLength: 16,
    shortName: 'CAKE',
    bankCode: '309',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Commonwealth Bank of Australia',
    vn_name: 'Commonwealth Bank of Australia',
    cardLength: 0,
    shortName: 'CBA',
    bankCode: '643',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Vietnam Construction Bank',
    vn_name: 'NH TMCP Xây dựng Việt Nam',
    bankId: '970444',
    bicCode: 'GTBAVNVN',
    atmBin: '970444',
    cardLength: 0,
    shortName: 'CBB',
    bankCode: '339',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'China Construction Bank Corporation',
    vn_name: 'China Construction Bank Corporation',
    cardLength: 0,
    shortName: 'CCBC',
    bankCode: '611',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'CTTC Quoc Te Chailease HSC',
    vn_name: 'CTTC Quoc Te Chailease HSC',
    cardLength: 0,
    shortName: 'Chailease',
    bankCode: '820',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'The Chase Manhattan Bank',
    vn_name: 'The Chase Manhattan Bank',
    cardLength: 0,
    shortName: 'CHASE',
    bankCode: '627',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'CIMB Bank Vietnam Limited',
    vn_name: 'Ngân hàng TNHH MTV CIMB Việt Nam',
    bankId: '422589',
    bicCode: 'CIBBVNVN',
    atmBin: '422589',
    cardLength: 0,
    shortName: 'CIMB',
    bankCode: '661',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'CitiBank HCM',
    vn_name: 'Citi Bank TP HCM',
    cardLength: 0,
    shortName: 'CitibankHCM',
    bankCode: '654',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Citibank Ha Noi',
    vn_name: 'Citi Bank Ha Noi',
    cardLength: 0,
    shortName: 'CitibankHN',
    bankCode: '605',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Co operative Bank of Viet Nam',
    vn_name: 'Ngân hàng Hợp tác xã Việt Nam',
    bankId: '970446',
    bicCode: 'COPBVNVN',
    atmBin: '970446',
    cardLength: 0,
    shortName: 'Co-opBank',
    bankCode: '901',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Co-Operation Bank of Viet Nam',
    vn_name: 'Ngân hàng Hợp tác Việt Nam',
    cardLength: 0,
    shortName: 'COOPBANK',
    bankCode: '901',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'The ChinaTrust Commercial Bank HCMC',
    vn_name: 'Ngân hàng CTBC CN TP Hồ Chí Minh',
    cardLength: 0,
    shortName: 'CTBC',
    bankCode: '629',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Cathay United Bank',
    vn_name: 'Ngân hàng Cathay',
    cardLength: 0,
    shortName: 'CTU',
    bankCode: '634',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'DEUTSCHE BANK',
    vn_name: 'DEUTSCHE BANK',
    cardLength: 0,
    shortName: 'DB',
    bankCode: '619',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'DBS Bank Ltd',
    vn_name: 'DBS Bank Ltd',
    cardLength: 0,
    shortName: 'DBS',
    bankCode: '650',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'The Development Bank of Singapore Limited',
    vn_name: 'DBS chi nhánh Thành phố Hồ Chí Minh',
    bankId: '796500',
    bicCode: 'DBSSVNVN',
    atmBin: '796500',
    cardLength: 16,
    shortName: 'DBS',
    bankCode: '650',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Dong A Commercial Joint stock Bank',
    vn_name: 'Ngân hàng Đông Á',
    bankId: '970406',
    bicCode: 'EACBVNVN',
    atmBin: '970406',
    cardLength: 16,
    shortName: 'Dong A Bank',
    bankCode: '304',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Vietnam Export Import Commercial Joint Stock Bank',
    vn_name: 'Ngân hàng Xuất nhập khẩu Việt Nam',
    bankId: '970431',
    bicCode: 'EBVIVNVN',
    atmBin: '970431',
    cardLength: 16,
    shortName: 'EXIMBANK',
    bankCode: '305',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'First Commercial Bank',
    vn_name: 'First Commercial Bank',
    cardLength: 0,
    shortName: 'FCNB',
    bankCode: '630',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'First Commercial Bank Ha Noi',
    vn_name: 'First Commercial Bank Ha Noi',
    cardLength: 0,
    shortName: 'FCNB HN',
    bankCode: '608',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Global Petro Commercial Joint Stock Bank',
    vn_name: 'Ngân hàng Dầu khí Toàn cầu',
    bankId: '970408',
    bicCode: 'GBNKVNVN',
    atmBin: '970408',
    cardLength: 20,
    shortName: 'GP Bank',
    bankCode: '320',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Housing Development Bank',
    vn_name: 'Ngân hàng Phát triển TP HCM',
    bankId: '970437',
    bicCode: 'HDBCVNVN',
    atmBin: '970437',
    cardLength: 20,
    shortName: 'HDBank',
    bankCode: '321',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Hong Leong Bank Viet Nam',
    vn_name: 'Ngân hàng Hong Leong Viet Nam',
    bankId: '970442',
    bicCode: 'HLBBVNVN',
    atmBin: '970442',
    cardLength: 20,
    shortName: 'HLO',
    bankCode: '603',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Hua Nan Commercial Bank',
    vn_name: 'Hua Nan Commercial Bank',
    cardLength: 0,
    shortName: 'HNCB',
    bankCode: '640',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'The HongKong and Shanghai Banking Corporation',
    vn_name: 'NH TNHH Một Thành Viên HSBC Việt Nam',
    bankId: '458761',
    bicCode: 'HSBCVNVN',
    atmBin: '458761',
    cardLength: 0,
    shortName: 'HSBC',
    bankCode: '617',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'NH The Hongkong and Shanghai',
    vn_name: 'Ngân hàng The Hongkong và Thượng Hải',
    cardLength: 0,
    shortName: 'HSBC HN',
    bankCode: '645',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Industrial Bank of Korea',
    vn_name: 'Industrial Bank of Korea',
    bankId: '970455',
    bicCode: 'IBKHVNVN',
    atmBin: '970455',
    cardLength: 0,
    shortName: 'IBK',
    bankCode: '641',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Industrial Bank of Korea HCMC',
    vn_name: 'Industrial Bank of Korea CN TP. HCM',
    bankId: '970456',
    bicCode: 'IBKSVNVN',
    atmBin: '970456',
    cardLength: 0,
    shortName: 'IBK HCM',
    bankCode: '641',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'ICB of China CN Ha Noi',
    vn_name: 'ICB of China CN Ha Noi',
    cardLength: 0,
    shortName: 'ICB',
    bankCode: '649',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Indovina Bank',
    vn_name: 'Indovina Bank',
    bankId: '970434',
    bicCode: 'IABBVNVN',
    atmBin: '888999',
    cardLength: 0,
    shortName: 'IVB',
    bankCode: '502',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'KBank',
    vn_name: 'Đại chúng TNHH Kasikornbank - Chi nhánh TP. HCM',
    bankId: '668888',
    cardLength: 0,
    shortName: 'KBank',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Kookmin Bank HCMC',
    vn_name: 'Ngân hàng Kookmin – CN TP. HCM',
    bankId: '970463',
    bicCode: 'CZNBVNVN',
    atmBin: '970463',
    cardLength: 0,
    shortName: 'KBHCM',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Kookmin Bank Hanoi',
    vn_name: 'Ngân hàng Kookmin - CN Hà Nội',
    bankId: '970462',
    bicCode: 'CZNBVNVN',
    atmBin: '970462',
    cardLength: 0,
    shortName: 'KBHN',
    bankCode: '631',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Kho Bac Nha Nuoc',
    vn_name: 'Kho Bạc Nhà Nước',
    cardLength: 0,
    shortName: 'KBNN',
    bankCode: '701',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Korea Exchange Bank',
    vn_name: 'Korea Exchange Bank',
    cardLength: 0,
    shortName: 'KEB',
    bankCode: '626',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'KEB Hana Bank of Ho Chi Minh',
    vn_name: 'Ngân hàng KEB Hana Chi nhánh TP.Hồ Chí Minh',
    bankId: '970466',
    bicCode: ' ',
    atmBin: '970466',
    cardLength: 16,
    shortName: 'KEBHANAHCM',
    bankCode: '656',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'KEB Hana Bank of Ha Noi',
    vn_name: 'Ngân hàng KEB Hana Chi nhánh Hà Nội',
    bankId: '970467',
    bicCode: ' ',
    atmBin: '970467',
    cardLength: 16,
    shortName: 'KEBHANAHN',
    bankCode: '626',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Kien Long Commercial Joint Stock Bank',
    vn_name: 'Ngân hàng Kiên Long',
    bankId: '970452',
    bicCode: 'KLBKVNVN',
    atmBin: '970452',
    cardLength: 16,
    shortName: 'Kienlongbank',
    bankCode: '353',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Kookmin Bank',
    vn_name: 'Ngân hàng Kookmin',
    cardLength: 0,
    shortName: 'KMB',
    bankCode: '631',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Lien Viet Post Bank',
    vn_name: 'Ngan hàng TMCP Bưu điện Liên Việt',
    bankId: '970449',
    bicCode: 'LVBKVNVN',
    atmBin: '970449',
    cardLength: 0,
    shortName: 'LIENVIETPOSTBANK',
    bankCode: '357',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Liobank by OCB',
    vn_name: 'Liobank by OCB',
    bankId: '963369',
    bicCode: 'ORCOVNVN',
    atmBin: '963369',
    cardLength: 16,
    shortName: 'Liobank by OCB',
    bankCode: '333',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Mirae Asset Finance Company (Vietnam) Limited',
    vn_name: 'Công ty Tài chính TNHH MTV Mirae Asset',
    bankId: '977777',
    cardLength: 16,
    shortName: 'MAFC',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Maritime Bank',
    vn_name: 'Ngân hàng Hàng Hải Việt Nam',
    bankId: '970426',
    bicCode: 'MCOBVNVN',
    atmBin: '970426',
    cardLength: 16,
    shortName: 'Maritime Bank, MSB',
    bankCode: '302',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Maybank',
    vn_name: 'Malayan Banking Berhad',
    cardLength: 0,
    shortName: 'Maybank',
    bankCode: '609',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Military Commercial Joint stock Bank',
    vn_name: 'Ngân hàng Quân Đội',
    bankId: '970422',
    bicCode: 'MSCBVNVN',
    atmBin: '970422',
    cardLength: 16,
    shortName: 'MB Bank, MB',
    bankCode: '311',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Malayan Banking Berhad',
    vn_name: 'Malayan Banking Berhad',
    cardLength: 0,
    shortName: 'MBB',
    bankCode: '635',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Mizuho Corporate Bank - TP HCM',
    vn_name: 'Mizuho Corporate Bank - TP HCM',
    cardLength: 0,
    shortName: 'MCB_HCM',
    bankCode: '639',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Mega ICBC Bank',
    vn_name: 'Mega ICBC Bank',
    cardLength: 0,
    shortName: 'MICB',
    bankCode: '623',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Mizuho Bank',
    vn_name: 'Mizuho Corporate Bank',
    cardLength: 0,
    shortName: 'Mizuho Bank',
    bankCode: '613',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Nam A Commercial Joint stock Bank',
    vn_name: 'Ngân hàng Nam Á',
    bankId: '970428',
    bicCode: 'NAMAVNVN',
    atmBin: '970428',
    cardLength: 0,
    shortName: 'Nam A Bank',
    bankCode: '306',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'National Citizen Bank',
    vn_name: 'Ngân hàng Quốc Dân',
    bankId: '970419',
    bicCode: 'NVBAVNVN',
    atmBin: '970419',
    cardLength: 16,
    shortName: 'NCB',
    bankCode: '352',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'NongHyup Bank Hanoi Branch',
    vn_name: 'Ngân hàng Nonghyup – Chi nhánh Hà Nội',
    bankId: '801011',
    bicCode: 'NONGVNVN',
    atmBin: '801011',
    cardLength: 0,
    shortName: 'NHB HN',
    bankCode: '662',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Oversea - Chinese Banking Corporation',
    vn_name: 'Oversea - Chinese Bank',
    cardLength: 0,
    shortName: 'OCBC',
    bankCode: '625',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Ocean Bank',
    vn_name: 'Ngân hàng Đại Dương',
    bankId: '970414',
    bicCode: 'OJBAVNVN',
    atmBin: '970414',
    cardLength: 20,
    shortName: 'Ocean Bank',
    bankCode: '319',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Orient Commercial Joint Stock Bank',
    vn_name: 'Ngân hàng TMCP Phương Đông',
    bankId: '970448',
    bicCode: 'ORCOVNVN',
    atmBin: '970448',
    cardLength: 16,
    shortName: 'Oricombank, OCB',
    bankCode: '333',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Petrolimex group commercial Joint stock Bank',
    vn_name: 'Ngân hàng Xăng dầu Petrolimex',
    bankId: '970430',
    bicCode: 'PGBLVNVN',
    atmBin: '970430',
    cardLength: 16,
    shortName: 'PG Bank',
    bankCode: '341',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'PVcombank',
    vn_name: 'NH TMCP Đại Chúng Việt Nam',
    bankId: '970412',
    bicCode: 'WBVNVNVN',
    atmBin: '970412',
    cardLength: 16,
    shortName: 'PVcombank',
    bankCode: '360',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Quy tin dung co so',
    vn_name: 'Quỹ tín dụng cơ sở',
    cardLength: 0,
    shortName: 'QTDCS',
    bankCode: '902',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Saigon Thuong Tin Commercial Joint Stock Bank',
    vn_name: 'Ngân hàng Sài Gòn Thương Tín',
    bankId: '970403',
    bicCode: 'SGTTVNVN',
    atmBin: '970403',
    cardLength: 16,
    shortName: 'Sacombank',
    bankCode: '303',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Saigon Bank for Industry and Trade',
    vn_name: 'Ngân hàng Sài Gòn Công Thương',
    bankId: '970400',
    bicCode: 'SBITVNVN',
    atmBin: '161087',
    cardLength: 16,
    shortName: 'Saigonbank',
    bankCode: '308',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'State Bank of Vietnam',
    vn_name: 'Ngân Hàng Nhà Nước Việt Nam',
    cardLength: 0,
    shortName: 'SBV',
    bankCode: '101',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Saigon Commercial Joint Stock Bank',
    vn_name: 'Ngân hàng TMCP Sài Gòn',
    bankId: '970429',
    bicCode: 'SACLVNVN',
    atmBin: '970429',
    cardLength: 16,
    shortName: 'SCB',
    bankCode: '334',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'The Shanghai Commercial & Savings Bank CN Dong Nai',
    vn_name: 'The Shanghai Commercial & Savings Bank CN Đồng Nai',
    cardLength: 0,
    shortName: 'SCSB',
    bankCode: '606',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Standard Chartered Bank',
    vn_name: 'Ngân hàng TNHH MTV Standard Chartered Việt Nam',
    bankId: '970410',
    bicCode: 'SCBLVNVN',
    atmBin: '970410',
    cardLength: 0,
    shortName: 'SCVN',
    bankCode: '604',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'South East Asia Commercial Joint stock  Bank',
    vn_name: 'Ngân hàng TMCP Đông Nam Á',
    bankId: '970440',
    bicCode: 'SEAVVNVN',
    atmBin: '970468',
    cardLength: 16,
    shortName: 'SeABank',
    bankCode: '317',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Saigon - Hanoi Commercial Joint Stock Bank',
    vn_name: 'Ngân hàng Sài Gòn - Hà Nội',
    bankId: '970443',
    bicCode: 'SHBAVNVN',
    atmBin: '970443',
    cardLength: 16,
    shortName: 'SHB',
    bankCode: '348',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Shinhan Bank',
    vn_name: 'Ngân hàng TNHH MTV Shinhan Việt Nam',
    bankId: '970424',
    bicCode: 'SHBKVNVN',
    atmBin: '970424',
    cardLength: 0,
    shortName: 'Shinhan Bank',
    bankCode: '616',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'The Siam Commercial Public Bank',
    vn_name: 'Ngân hàng The Siam Commercial Public',
    cardLength: 0,
    shortName: 'SIAM',
    bankCode: '600',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Sumitomo Mitsui Banking Corporation HCMC',
    vn_name: 'Sumitomo Mitsui Banking Corporation HCM',
    cardLength: 0,
    shortName: 'SMBC',
    bankCode: '636',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Sumitomo Mitsui Banking Corporation HN',
    vn_name: 'Sumitomo Mitsui Banking Corporation HN',
    cardLength: 0,
    shortName: 'SMBC HN',
    bankCode: '936',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'SinoPac Bank',
    vn_name: 'Ngân hàng SinoPac',
    cardLength: 0,
    shortName: 'SPB',
    bankCode: '632',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Taipei Fubon Commercial Bank Ha Noi',
    vn_name: 'Taipei Fubon Commercial Bank Ha Noi',
    cardLength: 0,
    shortName: 'TFCBHN',
    bankCode: '642',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Taipei Fubon Commercial Bank TP Ho Chi Minh',
    vn_name: 'Taipei Fubon Commercial Bank TP Ho Chi Minh',
    cardLength: 0,
    shortName: 'TFCBTPHCM',
    bankCode: '651',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Ubank by VPBank',
    vn_name: 'Ngân hàng số Ubank by VPBank',
    bankId: '546035',
    bicCode: 'UBVPVNVN',
    atmBin: '546035',
    cardLength: 16,
    shortName: 'Ubank',
    bankCode: '309',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'UMEE by Kienlongbank',
    vn_name: 'Ngân hàng số UMEE by Kienlongbank',
    bankId: '963399',
    bicCode: 'KLBKVNVN',
    atmBin: '963399',
    cardLength: 20,
    shortName: 'UMEE',
    bankCode: '353',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'United Oversea Bank',
    vn_name: 'United Oversea Bank',
    bankId: '970458',
    bicCode: 'UOVBVNVN',
    atmBin: '970458',
    cardLength: 0,
    shortName: 'UOB',
    bankCode: '618',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Vietnam Bank for Social Policies',
    vn_name: 'Ngân hàng Chính sách xã hội Việt Nam',
    bankId: '999888',
    atmBin: '999888',
    cardLength: 0,
    shortName: 'VBSP',
    bankCode: '207',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Vietnam Development Bank',
    vn_name: 'Ngân hàng Phát triển Việt Nam',
    cardLength: 0,
    shortName: 'VDB',
    bankCode: '208',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Vietnam International Commercial Joint Stock Bank',
    vn_name: 'Ngân hàng Quốc tế',
    bankId: '970441',
    bicCode: 'VNIBVNVN',
    atmBin: '970441',
    cardLength: 0,
    shortName: 'VIB',
    bankCode: '314',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'VID public',
    vn_name: 'Ngân hàng VID Public',
    bankId: '970439',
    bicCode: 'VIDPVNVN',
    atmBin: '970439',
    cardLength: 16,
    shortName: 'VID public',
    bankCode: '501',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Ngan hang Viet Hoa',
    vn_name: 'Ngân hàng Việt Hoa',
    cardLength: 0,
    shortName: 'Viet Hoa Bank',
    bankCode: '324',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'Viet A Commercial Joint Stock Bank',
    vn_name: 'Ngân hàng Việt Á',
    bankId: '970427',
    bicCode: 'VNACVNVN',
    atmBin: '970427',
    cardLength: 0,
    shortName: 'VIETABANK',
    bankCode: '355',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Vietnam Thương tin Commercial Joint Stock Bank',
    vn_name: 'Ngân hàng Việt Nam Thương Tín',
    bankId: '970433',
    bicCode: 'VNTTVNVN',
    atmBin: '970433',
    cardLength: 16,
    shortName: 'VIETBANK',
    bankCode: '356',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'BanViet Commercial Jont stock Bank',
    vn_name: 'NHTMCP Bản Việt',
    bankId: '970454',
    bicCode: 'VCBCVNVN',
    atmBin: '970454',
    cardLength: 16,
    shortName: 'VietCapital Bank',
    bankCode: '327',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Timo by Ban Viet Bank',
    vn_name: 'NHTMCP Bản Việt Timo',
    bankId: '963388',
    atmBin: '963388',
    cardLength: 16,
    shortName: 'VietCapital Bank Timo',
    bankCode: '327',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Vietnam Joint Stock Commercial Bank for Industry and Trade',
    vn_name: 'Ngân hàng Công Thương Việt Nam',
    bankId: '970415',
    bicCode: 'ICBVVNVN',
    atmBin: '970415',
    cardLength: 16,
    shortName: 'VietinBank',
    bankCode: '201',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Viettel Money',
    vn_name: 'Viettel Money',
    bankId: '971005',
    cardLength: 16,
    shortName: 'Viettel Money',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'VNPT Money',
    vn_name: 'VNPT Money',
    bankId: '971011',
    cardLength: 16,
    shortName: 'VNPT Money',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Vietnam prosperity Joint stock commercial Bank',
    vn_name: 'Ngân hàng Thương mại cổ phần Việt Nam Thịnh Vượng',
    bankId: '970432',
    bicCode: 'VPBKVNVN',
    atmBin: '970432',
    cardLength: 16,
    shortName: 'VPBank',
    bankCode: '309',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Vietnam - Russia Bank',
    vn_name: 'Ngân hàng Liên doanh Việt Nga',
    bankId: '970421',
    bicCode: 'VRBAVNVN',
    atmBin: '970421',
    cardLength: 16,
    shortName: 'VRB',
    bankCode: '505',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
  {
    en_name: 'Ngan hang Vung Tau',
    vn_name: 'Ngân hàng Vũng Tàu',
    cardLength: 0,
    shortName: 'Vung Tau',
    bankCode: '315',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'WOORI BANK HCMC',
    vn_name: 'Ngân hàng Woori Bank HCM',
    cardLength: 0,
    shortName: 'WHHCM',
    bankCode: '637',
    napasSupported: false,
    status: 'C',
    channel: 'IBFT',
  },
  {
    en_name: 'WOORI BANK Hanoi',
    vn_name: 'Ngân hàng Woori Bank Hà Nội',
    bankId: '970457',
    bicCode: 'HVBKVNVN',
    atmBin: '970457',
    cardLength: 0,
    shortName: 'WHHN',
    bankCode: '624',
    type: 'ACC',
    napasSupported: true,
    status: 'A',
    channel: 'IBFT',
  },
];
