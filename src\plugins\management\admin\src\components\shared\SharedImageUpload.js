const React = require('react');
const { useState } = React;
const { Upload, message, Progress } = require('antd');
const { Plus, UploadIcon, X, Wifi, WifiOff } = require('lucide-react');
const styled = require('styled-components');
const {
  useUpload,
  useNetworkCheck,
  UPLOAD_CONFIG,
} = require('../../hooks/useUpload.js');

const UploadContainer = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 10px;
  flex-wrap: wrap;
`;

const ImagePreviewContainer = styled.div`
  position: relative;
  width: 102px;
  height: 102px;
  margin-inline-end: 8px;
  margin-bottom: 8px;
  background-color: rgba(0, 0, 0, 0.02);
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const RemoveButton = styled.button`
  position: absolute;
  top: 4px;
  right: 4px;
  width: 20px;
  height: 20px;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  z-index: 1;

  &:hover {
    background: rgba(0, 0, 0, 0.7);
  }
`;

const UploadButton = styled.div`
  width: 102px;
  height: 102px;
  background-color: rgba(0, 0, 0, 0.02);
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  transition: border-color 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;

  &:hover {
    border-color: #1890ff;
  }

  .upload-text {
    font-size: 12px;
    color: #666;
    margin-top: 8px;
  }
`;

const SharedImageUpload = ({
  value = [],
  onChange,
  maxCount = 1,
  accept = 'image/*',
  disabled = false,
  uploadText = 'Tải lên',
  beforeUpload,
}) => {
  const { validateFile } = useUpload();
  const { checkConnectivity } = useNetworkCheck();
  const handleUploadChange = (info) => {
    let { fileList } = info;

    // Ensure each file has the correct status and uid
    fileList = fileList.map((file) => {
      if (!file.uid) {
        file.uid = `upload-${Date.now()}-${Math.random()}`;
      }
      if (!file.status) {
        file.status = 'done';
      }
      return file;
    });

    // Add new files to existing files
    const existingFiles = value || [];
    const newFiles = [...existingFiles, ...fileList];

    // Limit the number of files if maxCount is set
    const limitedFiles = maxCount ? newFiles.slice(0, maxCount) : newFiles;

    onChange?.(limitedFiles);
  };

  const handleRemove = (fileToRemove) => {
    const existingFiles = value || [];
    const newFileList = existingFiles.filter(
      (file) => file.uid !== fileToRemove.uid
    );
    onChange?.(newFileList);
  };

  const defaultBeforeUpload = async (file) => {
    // Use centralized validation
    const validation = validateFile(file);
    if (!validation.valid) {
      message.error(validation.error);
      return false;
    }

    // Check network connectivity
    const isConnected = await checkConnectivity();
    if (!isConnected) {
      message.error(
        'Không có kết nối mạng. Vui lòng kiểm tra kết nối internet.'
      );
      return false;
    }

    return false; // Prevent auto upload, we handle it manually
  };

  const fileList = value || [];
  const showUploadButton = !maxCount || fileList.length < maxCount;

  return React.createElement(
    UploadContainer,
    null,
    // Display uploaded images
    fileList.map((file) => {
      // Get image source for preview
      let imageSrc = '';

      if (file.url) {
        imageSrc = file.url;
      } else if (file.originFileObj) {
        imageSrc = URL.createObjectURL(file.originFileObj);
      }

      if (!imageSrc) {
        return null;
      }

      return React.createElement(
        ImagePreviewContainer,
        { key: file.uid },
        React.createElement('img', { src: imageSrc, alt: 'preview' }),
        !disabled &&
          React.createElement(
            RemoveButton,
            { onClick: () => handleRemove(file) },
            React.createElement(X, { size: 12 })
          )
      );
    }),
    // Upload button
    showUploadButton &&
      !disabled &&
      React.createElement(
        Upload,
        {
          listType: 'picture-card',
          fileList: [],
          onChange: handleUploadChange,
          beforeUpload: beforeUpload || defaultBeforeUpload,
          customRequest: ({ onSuccess }) => {
            // Custom request to prevent actual upload
            setTimeout(() => {
              onSuccess?.('ok');
            }, 0);
          },
          accept: accept,
          multiple: maxCount > 1,
          showUploadList: false,
          disabled: disabled,
        },
        React.createElement(
          UploadButton,
          null,
          React.createElement(UploadIcon, { size: 16 }),
          React.createElement('div', { className: 'upload-text' }, uploadText)
        )
      )
  );
};

module.exports = SharedImageUpload;
