const React = require('react');
const { Search: SearchIcon } = require('lucide-react');
const { SearchContainer, SearchInput } = require('./StyledComponents');

const SearchBar = ({
  placeholder = 'Tìm kiếm...',
  value,
  onChange,
  className,
}) => {
  return React.createElement(
    SearchContainer,
    { className: className },
    React.createElement(SearchIcon, null),
    React.createElement(SearchInput, {
      type: 'text',
      placeholder: placeholder,
      value: value,
      onChange: (e) => onChange(e.target.value),
    })
  );
};

module.exports = SearchBar;
