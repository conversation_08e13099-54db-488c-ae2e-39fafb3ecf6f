const React = require('react');
const { Image } = require('antd');
const { ImageContainer, PlaceholderContainer } = require('./StyledComponents');

const ImageDisplay = ({
  src,
  alt = 'Image',
  size = 40,
  placeholder = 'Không có',
  preview = true,
  previewSrc,
}) => {
  if (!src) {
    return React.createElement(
      PlaceholderContainer,
      { size: size },
      placeholder
    );
  }

  return React.createElement(
    ImageContainer,
    { size: size },
    React.createElement(Image, {
      src: src,
      alt: alt,
      preview: preview
        ? {
            src: previewSrc || src,
          }
        : false,
    })
  );
};

module.exports = ImageDisplay;
