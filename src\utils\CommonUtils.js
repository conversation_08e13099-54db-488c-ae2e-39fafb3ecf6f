'use strict';

/**
 * Format price function for Vietnamese currency
 * @param {number|string} price - The price to format
 * @returns {string} Formatted price string with Vietnamese currency
 */
const formatPrice = (price) => {
  if (price === null || price === undefined) return '0 ₫';
  
  // Convert to number if it's a string
  const numPrice = typeof price === 'string' ? parseFloat(price) : price;
  
  // Handle invalid numbers
  if (isNaN(numPrice)) return '0 ₫';
  
  // Format with Vietnamese locale and add currency symbol
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(numPrice);
};

/**
 * Format price without currency symbol (just number formatting)
 * @param {number|string} price - The price to format
 * @returns {string} Formatted price string without currency
 */
const formatNumber = (price) => {
  if (price === null || price === undefined) return '0';
  
  // Convert to number if it's a string
  const numPrice = typeof price === 'string' ? parseFloat(price) : price;
  
  // Handle invalid numbers
  if (isNaN(numPrice)) return '0';
  
  // Format with Vietnamese locale
  return new Intl.NumberFormat('vi-VN').format(numPrice);
};

/**
 * Parse formatted price string back to number
 * @param {string} formattedPrice - The formatted price string
 * @returns {number} Parsed number
 */
const parsePrice = (formattedPrice) => {
  if (!formattedPrice || typeof formattedPrice !== 'string') return 0;
  
  // Remove currency symbols and spaces, then parse
  const cleanPrice = formattedPrice
    .replace(/[₫\s]/g, '')
    .replace(/\./g, '')
    .replace(/,/g, '.');
  
  const parsed = parseFloat(cleanPrice);
  return isNaN(parsed) ? 0 : parsed;
};

/**
 * Format percentage
 * @param {number} value - The percentage value (0-1 or 0-100)
 * @param {boolean} isDecimal - Whether the input is decimal (0-1) or percentage (0-100)
 * @returns {string} Formatted percentage string
 */
const formatPercentage = (value, isDecimal = true) => {
  if (value === null || value === undefined) return '0%';
  
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numValue)) return '0%';
  
  const percentage = isDecimal ? numValue * 100 : numValue;
  return `${percentage.toFixed(1)}%`;
};

/**
 * Truncate text with ellipsis
 * @param {string} text - The text to truncate
 * @param {number} maxLength - Maximum length before truncation
 * @returns {string} Truncated text
 */
const truncateText = (text, maxLength = 50) => {
  if (!text || typeof text !== 'string') return '';
  
  if (text.length <= maxLength) return text;
  
  return text.substring(0, maxLength) + '...';
};

/**
 * Format date for Vietnamese locale
 * @param {Date|string} date - The date to format
 * @param {string} format - Format type: 'short', 'long', 'datetime'
 * @returns {string} Formatted date string
 */
const formatDate = (date, format = 'short') => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  if (isNaN(dateObj.getTime())) return '';
  
  const options = {
    short: { day: '2-digit', month: '2-digit', year: 'numeric' },
    long: { day: '2-digit', month: 'long', year: 'numeric' },
    datetime: { 
      day: '2-digit', 
      month: '2-digit', 
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }
  };
  
  return new Intl.DateTimeFormat('vi-VN', options[format] || options.short).format(dateObj);
};

/**
 * Generate random string
 * @param {number} length - Length of the random string
 * @param {string} charset - Character set to use
 * @returns {string} Random string
 */
const generateRandomString = (length = 6, charset = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ') => {
  let result = '';
  for (let i = 0; i < length; i++) {
    result += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return result;
};

/**
 * Validate Vietnamese phone number
 * @param {string} phone - Phone number to validate
 * @returns {boolean} Whether the phone number is valid
 */
const isValidVietnamesePhone = (phone) => {
  if (!phone || typeof phone !== 'string') return false;
  
  // Remove spaces and special characters
  const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
  
  // Vietnamese phone number patterns
  const patterns = [
    /^(\+84|84|0)(3[2-9]|5[6|8|9]|7[0|6-9]|8[1-6|8|9]|9[0-4|6-9])[0-9]{7}$/,
    /^(03[2-9]|05[6|8|9]|07[0|6-9]|08[1-6|8|9]|09[0-4|6-9])[0-9]{7}$/
  ];
  
  return patterns.some(pattern => pattern.test(cleanPhone));
};

module.exports = {
  formatPrice,
  formatNumber,
  parsePrice,
  formatPercentage,
  truncateText,
  formatDate,
  generateRandomString,
  isValidVietnamesePhone,
};
