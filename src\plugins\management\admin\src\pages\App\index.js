const React = require('react');
const { useState } = React;
const { Routes, Route } = require('react-router-dom');
const styled = require('styled-components');
const Sidebar = require('../../components/Sidebar').default;
const DashboardHeader = require('../../components/DashboardHeader').default;

// Import Be Vietnam Pro font
require('@fontsource/be-vietnam-pro/100.css');
require('@fontsource/be-vietnam-pro/200.css');
require('@fontsource/be-vietnam-pro/300.css');
require('@fontsource/be-vietnam-pro/400.css');
require('@fontsource/be-vietnam-pro/500.css');
require('@fontsource/be-vietnam-pro/600.css');
require('@fontsource/be-vietnam-pro/700.css');
require('@fontsource/be-vietnam-pro/800.css');
require('@fontsource/be-vietnam-pro/900.css');

// Import global styles
require('../../styles/global.css');

// Import components
const ProductManagement = require('../../components/ProductManagement').default;
const OrderManagement = require('../../components/OrderManagement').default;
const CategoryManagement =
  require('../../components/CategoryManagement').default;
const BrandManagement = require('../../components/BrandManagement').default;
const NewsCategories = require('../../components/NewsCategories').default;
const NewsManagement = require('../../components/NewsManagement').default;

// Import pages
const WithdrawalList = require('../../components/WithdrawalList').default;
const AffiliateOverview = require('../../components/AffiliateOverview').default;
const PromotionManagement =
  require('../../components/PromotionManagement').default;
const { UserList } = require('../../components/UserList');
const Settings = require('../../components/Settings').default;
const CommissionList = require('../../components/CommissionList').default;
const Dashboard = require('../../components/Dashboard').default;

// TODO: Create detail components later
// const OrderDetail = require('../../components/OrderDetail').default;
// const UserDetail = require('../../components/UserDetail').default;

// Simple NotFound component
const NotFound = () => {
  return React.createElement(
    'div',
    {
      style: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '50vh',
        fontFamily: 'Be Vietnam Pro, sans-serif',
      },
    },
    [
      React.createElement(
        'h1',
        {
          key: 'h1',
          style: { fontSize: '48px', color: '#6b7280', margin: '0 0 16px 0' },
        },
        '404'
      ),
      React.createElement(
        'p',
        {
          key: 'p',
          style: { fontSize: '18px', color: '#9ca3af', margin: 0 },
        },
        'Trang không tìm thấy'
      ),
    ]
  );
};

const ManagementContainer = styled.div`
  font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI',
    'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans',
    'Helvetica Neue', sans-serif !important;

  * {
    font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI',
      'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans',
      'Helvetica Neue', sans-serif !important;
  }

  .ant-layout {
    min-height: 100vh;
    background: #f8fafc;
    gap: 20px;
  }

  .ant-layout-sider {
    background: #ffffff;
  }

  .ant-menu {
    border-right: none;
    background: transparent;
  }

  .ant-menu-item {
    margin: 4px 8px;
    border-radius: 8px;
    height: 48px;
    line-height: 48px;
    display: flex;
    align-items: center;
  }

  .ant-menu-item-selected {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  .ant-menu-item:hover {
    background: #f0f2ff;
    color: #667eea;
  }

  .ant-menu-item-selected:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  /* Sidebar Styles */
  .sidebar {
    background: #ffffff;
    height: 100vh;
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s ease;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
    overflow: hidden;
  }

  .sidebar.collapsed {
    width: 70px;
  }

  .sidebar.expanded {
    width: 256px;
  }

  .sidebar-logo {
    padding: 16px;
  }

  .sidebar-logo-content {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .sidebar-logo-image {
    height: 32px;
    width: auto;
    max-width: 100%;
    object-fit: contain;
  }

  .sidebar-logo-icon {
    width: 32px;
    height: 32px;
    background-color: #2563eb;
    border-radius: 6px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-family: 'Be Vietnam Pro', sans-serif;
  }

  .sidebar-logo-text {
    margin-left: 8px;
    font-size: 20px;
    font-weight: bold;
    color: #2563eb;
    font-family: 'Be Vietnam Pro', sans-serif;
  }

  .sidebar-menu {
    padding: 16px;
  }

  .sidebar-menu-item {
    margin-bottom: 8px;
  }

  .sidebar-menu-button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
    border-radius: 8px;
    background-color: transparent;
    border: none;
    color: #374151;
    cursor: pointer;
    font-family: 'Be Vietnam Pro', sans-serif;
    font-size: 14px;
    transition: background-color 0.2s;
  }

  .sidebar-menu-button:hover {
    background-color: #f3f4f6;
  }

  .sidebar-menu-button.active {
    background-color: #eff6ff;
    color: #2563eb;
    font-weight: 500;
  }

  .sidebar-menu-button.active:hover {
    background-color: #eff6ff;
  }

  .sidebar-menu-button-content {
    display: flex;
    align-items: center;
  }

  .sidebar-menu-button-icon {
    width: 20px;
    height: 20px;
  }

  .sidebar-menu-button-text {
    margin-left: 12px;
    font-size: 14px;
    font-family: 'Be Vietnam Pro', sans-serif;
    opacity: 1;
    transition: opacity 0.3s ease, transform 0.3s ease;
    white-space: nowrap;
  }

  .sidebar.collapsed .sidebar-menu-button-text {
    opacity: 0;
    transform: translateX(-10px);
  }

  .sidebar-chevron {
    width: 16px;
    height: 16px;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
    opacity: 1;
  }

  .sidebar.collapsed .sidebar-chevron {
    opacity: 0;
    transform: scale(0.8);
  }

  .sidebar-chevron.rotated {
    transform: rotate(180deg);
  }

  .sidebar.collapsed .sidebar-chevron.rotated {
    transform: rotate(180deg) scale(0.8);
    opacity: 0;
  }

  .sidebar-submenu {
    margin-top: 4px;
    opacity: 1;
    transition: opacity 0.3s ease, max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    max-height: 500px;
    overflow: hidden;
  }

  .sidebar.collapsed .sidebar-submenu {
    opacity: 0;
    max-height: 0;
  }

  .sidebar-submenu-item {
    width: 100%;
    text-align: left;
    padding: 8px 8px 8px 40px;
    margin: 2px 0;
    font-size: 14px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    font-family: 'Be Vietnam Pro', sans-serif;
    display: flex;
    align-items: center;
    background-color: transparent;
    color: #374151;
    font-weight: normal;
    transition: all 0.2s;
    gap: 4px;
  }

  .sidebar-submenu-item:hover {
    background-color: #f3f4f6;
  }

  .sidebar-submenu-item.active {
    background-color: #eff6ff;
    color: #2563eb;
    font-weight: 500;
  }

  .sidebar-submenu-item.active:hover {
    background-color: #eff6ff;
  }

  .sidebar-submenu-item-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }

  /* Tooltip submenu styles */
  .sidebar-submenu-tooltip .ant-tooltip-inner {
    padding: 0 !important;
    background: transparent !important;
    box-shadow: none !important;
  }

  .sidebar-submenu-tooltip .ant-tooltip-arrow {
    display: none !important;
  }

  /* Tooltip menu styles */
  .sidebar-menu-tooltip .ant-tooltip-inner {
    background: #374151 !important;
    color: white !important;
    font-family: 'Be Vietnam Pro', sans-serif !important;
    font-size: 14px !important;
    border-radius: 6px !important;
    padding: 8px 12px !important;
  }
`;

const App = () => {
  // Lấy trạng thái collapsed từ localStorage, mặc định là false
  const [collapsed, setCollapsed] = useState(() => {
    const saved = localStorage.getItem('sidebar-collapsed');
    return saved ? JSON.parse(saved) : false;
  });

  // Lưu trạng thái collapsed vào localStorage khi thay đổi
  const toggleCollapsed = () => {
    const newCollapsed = !collapsed;
    setCollapsed(newCollapsed);
    localStorage.setItem('sidebar-collapsed', JSON.stringify(newCollapsed));
  };

  return React.createElement(
    ManagementContainer,
    { className: 'management-plugin' },
    React.createElement(
      'div',
      { style: { display: 'flex', minHeight: '100vh' } },
      [
        React.createElement(Sidebar, { key: 'sidebar', collapsed }),

        // Toggle Button
        React.createElement(
          'button',
          {
            key: 'toggle-button',
            onClick: toggleCollapsed,
            style: {
              position: 'fixed',
              top: '20px',
              left: collapsed ? '80px' : '265px',
              zIndex: 1000,
              background: '#ffffff',
              border: '1px solid #e2e8f0',
              borderRadius: '8px',
              padding: '8px',
              cursor: 'pointer',
              transition:
                'left 0.4s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s ease, transform 0.2s ease',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '32px',
              height: '32px',
            },
          },
          React.createElement(
            'span',
            {
              style: {
                fontSize: '12px',
                color: '#6b7280',
                transition: 'transform 0.3s ease',
                transform: collapsed ? 'rotate(0deg)' : 'rotate(180deg)',
              },
            },
            '→'
          )
        ),

        React.createElement(
          'div',
          {
            key: 'main-content',
            style: {
              marginLeft: collapsed ? '10px' : '200px',
              transition: 'margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
              flex: 1,
              minHeight: '100vh',
              background: '#f8fafc',
            },
          },
          [
            React.createElement(DashboardHeader, { key: 'header', collapsed }),

            React.createElement(
              'div',
              {
                key: 'content',
                style: {
                  paddingTop: '80px', // Space for fixed header
                },
              },
              React.createElement(Routes, null, [
                React.createElement(Route, {
                  key: 'index',
                  index: true,
                  element: React.createElement(Dashboard),
                }),
                React.createElement(Route, {
                  key: 'dashboard',
                  path: 'dashboard',
                  element: React.createElement(Dashboard),
                }),
                React.createElement(Route, {
                  key: 'orders',
                  path: 'orders',
                  element: React.createElement(OrderManagement),
                }),
                React.createElement(Route, {
                  key: 'users',
                  path: 'users',
                  element: React.createElement(UserList),
                }),
                React.createElement(Route, {
                  key: 'commissions',
                  path: 'commissions',
                  element: React.createElement(CommissionList),
                }),
                React.createElement(Route, {
                  key: 'withdrawals',
                  path: 'affiliate/withdrawals',
                  element: React.createElement(WithdrawalList),
                }),
                React.createElement(Route, {
                  key: 'affiliate-overview',
                  path: 'affiliate/overview',
                  element: React.createElement(AffiliateOverview),
                }),
                React.createElement(Route, {
                  key: 'news-categories',
                  path: 'news/categories',
                  element: React.createElement(NewsCategories),
                }),
                React.createElement(Route, {
                  key: 'news-articles',
                  path: 'news/articles',
                  element: React.createElement(NewsManagement),
                }),
                React.createElement(Route, {
                  key: 'product-categories',
                  path: 'products/categories',
                  element: React.createElement(CategoryManagement),
                }),
                React.createElement(Route, {
                  key: 'product-brands',
                  path: 'products/brands',
                  element: React.createElement(BrandManagement),
                }),
                React.createElement(Route, {
                  key: 'product-list',
                  path: 'products/list',
                  element: React.createElement(ProductManagement),
                }),
                React.createElement(Route, {
                  key: 'promotions',
                  path: 'products/promotions',
                  element: React.createElement(PromotionManagement),
                }),
                React.createElement(Route, {
                  key: 'settings',
                  path: 'settings/*',
                  element: React.createElement(Settings),
                }),
                React.createElement(Route, {
                  key: 'not-found',
                  path: '*',
                  element: React.createElement(NotFound),
                }),
              ])
            ),
          ]
        ),
      ]
    )
  );
};

module.exports = { App };
