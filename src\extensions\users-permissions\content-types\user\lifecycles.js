const {
  baseAfterCreate,
  baseAfterUpdate,
  baseAfterDelete,
} = require('../../../../utils/base/lifecycles');
const {
  MoneyToCreateSubAccount,
  Level,
  PREFIX_CODE,
  ColabStatus,
} = require('../../../../utils/constants');
const {
  calcUserLevel,
  sendNotiBaseOnLevel,
  queryUserById,
  updateLevelParent,
} = require('../../../../utils/utils');

async function createSubAccount(currentUser) {
  const {
    phone,
    name,
    address,
    district,
    province,
    ward,
    id,
    subBalance,
    numberOfSubAccount,
    sChildren,
  } = currentUser;
  if (subBalance < MoneyToCreateSubAccount) {
    return;
  }
  const pluginStore = await strapi.store({
    type: 'plugin',
    name: 'users-permissions',
  });

  const settings = await pluginStore.get({ key: 'advanced' });
  const role = await strapi
    .query('plugin::users-permissions.role')
    .findOne({ where: { type: settings.default_role } });

  const suffix = (numberOfSubAccount || 0) + 1;
  const res = await strapi.entityService.create(
    'plugin::users-permissions.user',
    {
      data: {
        phone: `${phone}-${suffix}`,
        name,
        provider: 'local',
        blocked: false,
        password: '123456',
        role: role.id,
        confirmed: true,
        verified: true,
        balance: 0,
        subBalance: 0,
        address,
        district,
        province,
        ward,
        level: Level.NewUser,
        fParent: id,
      },
    }
  );
  if (!res.id) {
    throw new Error('can not create sub account');
  }
  const res2 = await strapi.entityService.findMany(
    'api::identity-verification.identity-verification',
    { filters: { userId: id }, populate: '*' }
  );
  if (res2.length === 0) {
    throw new Error('can not find identity verification');
  }
  const oldIdentity = res2[0];
  await strapi.entityService.create(
    'api::identity-verification.identity-verification',
    {
      data: {
        userId: res.id,
        phone: res.phone,
        identityNumber: `${oldIdentity.identityNumber}(${suffix})`,
        frontIdentityCard: oldIdentity.frontIdentityCard.id,
        backIdentityCard: oldIdentity.backIdentityCard.id,
        accountNumber: oldIdentity.accountNumber,
        bank: oldIdentity.bank,
        accountName: oldIdentity.accountName,
      },
    }
  );
  const newChildren = sChildren ? `${sChildren},${res.id}` : `${res.id}`;

  const newReferCode = PREFIX_CODE + user.id;
  await strapi.entityService.update('plugin::users-permissions.user', id, {
    data: {
      subBalance: subBalance - MoneyToCreateSubAccount,
      numberOfSubAccount: numberOfSubAccount + 1,
      sChildren: newChildren,
      referCode: newReferCode,
    },
  });
}

module.exports = {
  afterCreate(event) {
    baseAfterCreate(event);
  },
  async afterUpdate(event) {
    const { params } = event;
    if (
      params?.data?.level === Level.NewUser &&
      params?.data?.colabStatus === ColabStatus.Approved
    ) {
      await strapi.db.query('plugin::users-permissions.user').update({
        where: { id: event.result.id },
        data: { level: Level.BronzeMember },
      });
      await updateLevelParent(params?.data);
    }
    baseAfterUpdate(event);
    // createSubAccount(event.result);
  },
  afterDelete(event) {
    baseAfterDelete(event);
  },
};
