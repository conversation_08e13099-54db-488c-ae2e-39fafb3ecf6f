const dayjs = require('dayjs');
const { Client } = require('pg');

const ttkoreaLocal = {
  host: 'localhost',
  port: 5432,
  database: 'ukg1',
  user: 'postgres',
  password: 'we.yolo2',
};

const ttkoreaTest = {
  host: '**************',
  port: 5432,
  database: 'ttkorea-test',
  user: 'postgres',
  password: 'Ttkorea123',
};

const ttkoreaProd = {
  host: '**************',
  port: 5432,
  database: 'ttkorea',
  user: 'postgres',
  password: 'Ttkorea123',
};

const updateCommissionInTableUser = async (postgresDB) => {
  const allUser = (
    await postgresDB.query(
      `SELECT status, first_order_at, id, balance
        FROM up_users
        ORDER BY id ASC;`
    )
  ).rows;
  console.log('🚀 ~ main ~ allUser:', allUser.length);
  for (const user of allUser) {
    // update user commission
    if (!user.commission) {
      await postgresDB.query(
        `UPDATE up_users SET commission='{"totalCommission":${parseInt(
          user.balance
        )},"withdrawalCommission":0,"managerCommission":0,"sharedCommission":0}'::jsonb WHERE id=${
          user.id
        };`
      );
    }
  }
};


async function main() {
  const postgresDB = new Client(ttkoreaLocal);
  await postgresDB.connect();

  const allUser = (
    await postgresDB.query(
      `SELECT status, first_order_at, id, refer_code
        FROM up_users
        WHERE status = 'inactive'
        ORDER BY id ASC;`
    )
  ).rows;
  console.log('🚀 ~ main ~ allUser:', allUser.length);

  const start = dayjs().startOf('month');
  const end = dayjs().endOf('month');
  for (const user of allUser) {
    const orders = (
      await postgresDB.query(
        `SELECT id
        FROM orders
        WHERE creator_id = '${user.id}'
            AND (order_status=2 or order_status=3 or order_status=5)
            AND created_at between to_timestamp(${start.unix()}) and to_timestamp(${end.unix()})
        ORDER BY id ASC;`
      )
    ).rows;
    if (orders.length) {
      if (user.first_order_at) {
        console.log(
          '🚀 ~ main ~ user:',
          user.id,
          orders[0].id,
          user.first_order_at
        );
        // const query = `UPDATE up_users SET status = 'active' WHERE id = '${user.id}';`;
        // await postgresDB.query(query);
      }
    }
  }

  await updateCommissionInTableUser(postgresDB);
  console.log("tetssss");
  await postgresDB.end();
}

main();
