const { Client } = require('pg');

const local = {
  host: 'localhost',
  port: 5432,
  database: 'davy2',
  user: 'huylv',
  password: '',
};

const davy = {
  host: 'localhost',
  port: 5432,
  database: 'ttkorea-production',
  user: 'postgres',
  password: 'postgres',
};

async function main() {
  const client = new Client(davy);
  await client.connect();

  const tables = (
    await client.query(
      "SELECT table_name FROM information_schema.tables WHERE table_schema='public' AND table_type='BASE TABLE';"
    )
  ).rows.map((o) => o.table_name);
  // await client.end();
  // return;

  const excludes = [
    'strapi_migrations',
    'sqlite_sequence',
    'strapi_database_schema',
    'strapi_core_store_settings',
    'strapi_webhooks',
    'admin_permissions_role_links',
    'strapi_api_token_permissions_token_links',
    'strapi_transfer_token_permissions_token_links',
    'files_related_morphs',
    'files_folder_links',
    'upload_folders_parent_links',
    'up_permissions_role_links',
    'up_users_role_links',
    'admin_users_roles_links',
  ];

  // await client.end();
  // return;
  for (const table of tables) {
    console.log(table);
    const columns = (
      await client.query(`SELECT * 
      FROM information_schema.columns 
      WHERE table_name = '${table}';`)
    ).rows;
    // change id column to type int4
    await client.query(`ALTER TABLE ${table} ALTER COLUMN id type int4;`);
    const columnToChanges = columns.filter(
      (o) => o.column_name.endsWith('_id') && o.udt_name === 'int8'
    );
    for (const col of columnToChanges) {
      console.log('🚀 ~ main ~63 ALTER TABLE:', col.column_name);
      await client.query(
        `ALTER TABLE ${table} ALTER COLUMN ${col.column_name} TYPE int4;`
      );
    }
    if (!excludes.includes(table)) {
      // if (table !== "courses") continue;
      for (const col of columns) {
        if (col.data_type === 'timestamp without time zone') {
          const rows = (
            await client.query(
              `SELECT ${col.column_name} FROM ${table} LIMIT 1`
            )
          ).rows;
          if (rows.length > 0) {
            const query = `
            UPDATE ${table}
            SET ${col.column_name} = 
              CASE 
              WHEN extract(epoch from ${col.column_name})::bigint > 100000000000 THEN
                TO_TIMESTAMP((extract(epoch from ${col.column_name})::bigint - 25200) / 1000)
              ELSE
                ${col.column_name}
            END;`;
            console.log(
              '🚀 ~ main ~ query 87: UPDATE ',
              table,
              col.column_name
            );
            await client.query(query);
          }
        }
      }
    }
  }
  await client.end();
}

main();
