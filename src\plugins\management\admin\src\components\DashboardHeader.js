const React = require('react');
const { useState, useEffect, useRef } = React;
const { Search, Bell, User, Package, Clock } = require('lucide-react');
const styled = require('styled-components');
const { Badge, Spin, Empty } = require('antd');
const { useFetchClient } = require('@strapi/helper-plugin');
const { useNavigate } = require('react-router-dom');

const HeaderContainer = styled.div`
  background: #ffffff;
  padding: 16px 24px;
  position: fixed;
  top: 0;
  right: 0;
  left: 256px;
  z-index: 999;
  transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  &.collapsed {
    left: 70px;
  }
`;

const HeaderContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const LeftSection = styled.div`
  display: flex;
  align-items: center;
`;

const BreadcrumbText = styled.span`
  color: #6b7280;
  font-size: 14px;
  font-family: 'Be Vietnam Pro', sans-serif;
`;

const RightSection = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const SearchContainer = styled.div`
  position: relative;
`;

const SearchIcon = styled(Search)`
  width: 16px;
  height: 16px;
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
`;

const SearchInput = styled.input`
  padding: 8px 16px 8px 40px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  font-family: 'Be Vietnam Pro', sans-serif;
  outline: none;
  transition: all 0.2s ease;
  width: 240px;

  &:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &::placeholder {
    color: #9ca3af;
  }
`;

const NotificationButton = styled.button`
  position: relative;
  padding: 8px;
  background: transparent;
  border: none;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f3f4f6;
  }
`;

const BellIcon = styled(Bell)`
  width: 20px;
  height: 20px;
  color: #6b7280;
`;

const NotificationContainer = styled.div`
  position: relative;
`;

const NotificationDropdown = styled.div`
  position: absolute;
  top: 100%;
  right: 0;
  width: 320px;
  max-height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  z-index: 1000;
  margin-top: 8px;
  display: ${(props) => (props.visible ? 'block' : 'none')};
`;

const NotificationHeader = styled.div`
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
`;

const NotificationTitle = styled.h4`
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  font-family: 'Be Vietnam Pro', sans-serif;
`;

const NotificationList = styled.div`
  max-height: 300px;
  overflow-y: auto;
`;

const NotificationItem = styled.div`
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
  }

  &:last-child {
    border-bottom: none;
  }
`;

const NotificationContent = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 12px;
`;

const NotificationIcon = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: #e6f7ff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
`;

const NotificationText = styled.div`
  flex: 1;
`;

const NotificationMessage = styled.div`
  font-size: 13px;
  color: #262626;
  font-family: 'Be Vietnam Pro', sans-serif;
  margin-bottom: 4px;
`;

const NotificationTime = styled.div`
  font-size: 12px;
  color: #8c8c8c;
  font-family: 'Be Vietnam Pro', sans-serif;
`;

const NotificationEmpty = styled.div`
  padding: 40px 16px;
  text-align: center;
  color: #8c8c8c;
  font-family: 'Be Vietnam Pro', sans-serif;
`;

const UserSection = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const UserAvatar = styled.div`
  width: 32px;
  height: 32px;
  background-color: #d1d5db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const UserIcon = styled(User)`
  width: 16px;
  height: 16px;
  color: #6b7280;
`;

const UserName = styled.span`
  font-size: 14px;
  color: #374151;
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 500;
`;

const DashboardHeader = ({ collapsed = false }) => {
  const { get } = useFetchClient();
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState([]);
  const [notificationCount, setNotificationCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [dropdownVisible, setDropdownVisible] = useState(false);

  // Fetch notifications
  const fetchNotifications = async () => {
    setLoading(true);
    try {
      // Fetch pending orders
      const { data: ordersData } = await get('/management/orders', {
        params: { status: 'Chờ xác nhận', pageSize: 10 },
      });

      const pendingOrders = ordersData?.data || [];

      // Create notifications from pending orders
      const orderNotifications = pendingOrders.map((order) => ({
        id: `order-${order.id}`,
        type: 'order',
        title: 'Đơn hàng mới',
        message: `Đơn hàng ${order.code} cần được xác nhận`,
        time: formatTimeAgo(order.createdAt),
        data: order,
      }));

      setNotifications(orderNotifications);
      setNotificationCount(orderNotifications.length);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  // Format time ago
  const formatTimeAgo = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60)
    );

    if (diffInMinutes < 1) return 'Vừa xong';
    if (diffInMinutes < 60) return `${diffInMinutes} phút trước`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} giờ trước`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} ngày trước`;
  };

  // Handle notification click
  const handleNotificationClick = (notification) => {
    if (notification.type === 'order') {
      navigate(`orders`);
    }
    setDropdownVisible(false);
  };

  // Fetch notifications on component mount and set up polling
  useEffect(() => {
    fetchNotifications();

    // Poll for new notifications every 30 seconds
    const interval = setInterval(fetchNotifications, 30000);

    return () => clearInterval(interval);
  }, []);

  // Handle click outside to close dropdown
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownVisible(false);
      }
    };

    if (dropdownVisible) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [dropdownVisible]);

  return React.createElement(
    HeaderContainer,
    { className: collapsed ? 'collapsed' : '' },
    React.createElement(HeaderContent, null, [
      React.createElement(LeftSection, { key: 'left' }),

      React.createElement(RightSection, { key: 'right' }, [
        React.createElement(SearchContainer, { key: 'search' }, [
          React.createElement(SearchIcon, { key: 'search-icon' }),
          React.createElement(SearchInput, {
            key: 'search-input',
            type: 'text',
            placeholder: 'Search...',
          }),
        ]),

        React.createElement(
          NotificationContainer,
          { key: 'notifications', ref: dropdownRef },
          [
            React.createElement(
              NotificationButton,
              {
                key: 'notification-button',
                onClick: () => setDropdownVisible(!dropdownVisible),
              },
              React.createElement(
                Badge,
                {
                  count: notificationCount,
                  size: 'small',
                  offset: [-2, 2],
                },
                React.createElement(BellIcon)
              )
            ),

            React.createElement(
              NotificationDropdown,
              { key: 'dropdown', visible: dropdownVisible },
              [
                React.createElement(
                  NotificationHeader,
                  { key: 'header' },
                  React.createElement(
                    NotificationTitle,
                    null,
                    `Thông báo (${notificationCount})`
                  )
                ),

                loading
                  ? React.createElement(
                      'div',
                      {
                        key: 'loading',
                        style: { padding: '20px', textAlign: 'center' },
                      },
                      React.createElement(Spin, { size: 'small' })
                    )
                  : notifications.length > 0
                  ? React.createElement(
                      NotificationList,
                      { key: 'list' },
                      notifications.map((notification) =>
                        React.createElement(
                          NotificationItem,
                          {
                            key: notification.id,
                            onClick: () =>
                              handleNotificationClick(notification),
                          },
                          React.createElement(NotificationContent, null, [
                            React.createElement(
                              NotificationIcon,
                              { key: 'icon' },
                              notification.type === 'order' &&
                                React.createElement(Package, {
                                  size: 16,
                                  color: '#1890ff',
                                }),
                              notification.type === 'user' &&
                                React.createElement(User, {
                                  size: 16,
                                  color: '#52c41a',
                                }),
                              notification.type === 'withdrawal' &&
                                React.createElement(Clock, {
                                  size: 16,
                                  color: '#faad14',
                                })
                            ),
                            React.createElement(
                              NotificationText,
                              { key: 'text' },
                              [
                                React.createElement(
                                  NotificationMessage,
                                  { key: 'message' },
                                  notification.message
                                ),
                                React.createElement(
                                  NotificationTime,
                                  { key: 'time' },
                                  notification.time
                                ),
                              ]
                            ),
                          ])
                        )
                      )
                    )
                  : React.createElement(
                      NotificationEmpty,
                      { key: 'empty' },
                      React.createElement(Empty, {
                        image: Empty.PRESENTED_IMAGE_SIMPLE,
                        description: 'Không có thông báo mới',
                      })
                    ),
              ]
            ),
          ]
        ),

        React.createElement(UserSection, { key: 'user' }, [
          React.createElement(
            UserAvatar,
            { key: 'avatar' },
            React.createElement(UserIcon)
          ),
          React.createElement(UserName, { key: 'name' }, 'Admin'),
        ]),
      ]),
    ])
  );
};

module.exports = DashboardHeader;
